/**
 * A {@link ElementComponent} that contains child {@link ElementComponent}s.
 *
 * @category User Interface
 */
export const ELEMENTTYPE_GROUP = 'group';

/**
 * A {@link ElementComponent} that displays an image.
 *
 * @category User Interface
 */
export const ELEMENTTYPE_IMAGE = 'image';

/**
 * A {@link ElementComponent} that displays text.
 *
 * @category User Interface
 */
export const ELEMENTTYPE_TEXT = 'text';

/**
 * Fit the content exactly to Element's bounding box.
 *
 * @category User Interface
 */
export const FITMODE_STRETCH = 'stretch';

/**
 * Fit the content within the Element's bounding box while preserving its Aspect Ratio.
 *
 * @category User Interface
 */
export const FITMODE_CONTAIN = 'contain';

/**
 * Fit the content to cover the entire Element's bounding box while preserving its Aspect Ratio.
 *
 * @category User Interface
 */
export const FITMODE_COVER = 'cover';
