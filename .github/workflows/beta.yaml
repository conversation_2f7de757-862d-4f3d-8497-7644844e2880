name: Beta

on:
  workflow_dispatch:
    inputs:
      force:
        description: 'Force release even if no changes detected'
        type: boolean
        default: false

permissions:
  contents: write

jobs:
  beta:
    runs-on: ubuntu-latest
    if: github.repository == 'playcanvas/engine' && github.ref_name == 'main'
    steps:
      - name: Generate app token
        id: app-token
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.APP_KEY }}

      - name: Check out code
        uses: actions/checkout@v5
        with:
          fetch-depth: 0
          token: ${{ steps.app-token.outputs.token }}

      - name: Configure Git User
        run: |
          git config --global user.email "playcanvas[bot]@users.noreply.github.com"
          git config --global user.name "PlayCanvas [bot]"
        shell: bash

      - name: Check for changes
        if: github.event.inputs.force == 'false'
        run: |
          last_tag=$(git describe --tags --abbrev=0)
          if ! git diff --quiet --exit-code $last_tag; then
            echo "Changes found since v$last_tag"
          else
            echo "No changes detected since v$last_tag"
            exit 1
          fi

      - name: Bump version
        run: |
          npm version prerelease --preid=beta

      - name: Push version
        run: |
          git push origin HEAD:${{ github.ref_name }}
          git push origin --tags
        shell: bash
