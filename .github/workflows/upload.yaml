name: Upload

on:
  workflow_dispatch:
  workflow_run:
    workflows: ["Publish"]
    types:
      - completed

jobs:
  upload:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'playcanvas' && (github.event_name == 'workflow_dispatch' && startsWith(github.ref, 'refs/tags/') || (github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success'))
    steps:
      - name: Download version
        if: github.event_name == 'workflow_run'
        uses: actions/download-artifact@v5
        with:
          name: version
          github-token: ${{ secrets.GITHUB_TOKEN }}
          run-id: ${{ github.event.workflow_run.id }}

      - name: Read version
        if: github.event_name == 'workflow_run'
        run: echo "VERSION=$(cat version.txt)" >> $GITHUB_ENV

      - name: Parse tag name
        if: github.event_name == 'workflow_dispatch'
        run: |
          TAG_NAME=${GITHUB_REF#refs/tags/}
          echo "VERSION=${TAG_NAME/v/}" >> $GITHUB_ENV

      - name: Upload to code.playcanvas.com
        run: |
          if ! curl -fsS -X POST -H "Content-Type: application/json" \
            -d '{ "engineVersion": "${{ env.VERSION }}" }' ${{ secrets.PUBLISH_ENDPOINT }}; then
            echo "Failed to publish to code.playcanvas.com"
            exit 1
          fi