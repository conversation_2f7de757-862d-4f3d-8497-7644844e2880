name: Publish

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
      - 'v[0-9]+.[0-9]+.[0-9]+-preview.[0-9]+'
      - 'v[0-9]+.[0-9]+.[0-9]+-beta.[0-9]+'

jobs:
  publish:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'playcanvas'
    steps:
      - name: Check out code
        uses: actions/checkout@v5

      - name: Set up Node.js 22.x
        uses: actions/setup-node@v5
        with:
          node-version: 22.x
          cache: 'npm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Parse tag name
        run: |
          TAG_NAME=${GITHUB_REF#refs/tags/}
          echo "TAG=${TAG_NAME}" >> $GITHUB_ENV
          echo "VERSION=${TAG_NAME/v/}" >> $GITHUB_ENV

      - name: Install Dependencies
        run: npm install

      - name: Build PlayCanvas
        run: npm run build

      - name: Run Publint
        run: npm run publint

      - name: Publish to npm
        run: |
          if [[ "${{ env.TAG }}" =~ "preview" ]]; then
            tag=preview
          elif [[ "${{ env.TAG }}" =~ "beta" ]]; then
            tag=beta
          else
            tag=latest
          fi
          npm publish --tag $tag
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Write version
        run: echo "${{ env.VERSION }}" > version.txt

      - name: Upload version
        uses: actions/upload-artifact@v4
        with:
          name: version
          path: version.txt