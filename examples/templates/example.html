<!-- N.B. All single quote strings starting with '@' and are in all caps denote placeholders for replacement code  -->
<html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <link rel="stylesheet" href="./main.css" />
        <title>'@TITLE'</title>
    </head>
    <body>
        <div id="app">
            <div id="appInner">
                <canvas id="application-canvas"></canvas>
            </div>
        </div>
        <script src="./polyfill.js"></script>
        <script type="importmap">
            {
                "imports": {
                    "playcanvas": '@ENGINE',
                    "fflate": "../../modules/fflate/esm/browser.js",
                    "examples/utils": "./utils.mjs",
                    "examples/observer": "./observer.mjs",
                    "examples/files": "./files.mjs"
                }
            }
        </script>
        <script type="module">
            import { ExampleLoader } from './loader.mjs';
            const loader = new ExampleLoader();

            Object.defineProperty(window, 'ready', { get: () => loader.ready });

            window.addEventListener('requestFiles', () => loader.sendRequestedFiles());
            window.addEventListener('stats', (event) => loader.setMiniStats(!!event.detail?.state));
            window.addEventListener('hotReload', () => loader.hotReload());
            window.addEventListener('destroy', () => loader.destroy());
            window.addEventListener('beforeunload', () => loader.exit(), false);

            document.addEventListener('DOMContentLoaded', () => {
                loader.start({
                    engineUrl: '@ENGINE',
                    fileNames: '@FILES',
                });
            });
        </script>
    </body>
</html>