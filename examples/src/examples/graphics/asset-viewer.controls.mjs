/**
 * @param {import('../../app/components/Example.mjs').ControlOptions} options - The options.
 * @returns {JSX.Element} The returned JSX Element.
 */
export const controls = ({ observer, ReactPCUI, React, jsx, fragment }) => {
    const { <PERSON>, Button } = ReactPCUI;
    return jsx(
        Panel,
        { headerText: 'Asset' },
        jsx(<PERSON><PERSON>, {
            text: 'Previous',
            onClick: () => observer.emit('previous')
        }),
        jsx(<PERSON><PERSON>, {
            text: 'Next',
            onClick: () => observer.emit('next')
        })
    );
};
