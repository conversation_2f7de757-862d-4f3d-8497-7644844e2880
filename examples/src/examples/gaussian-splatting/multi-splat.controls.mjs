/**
 * @param {import('../../app/components/Example.mjs').ControlOptions} options - The options.
 * @returns {JSX.Element} The returned JSX Element.
 */
export const controls = ({ observer, ReactPCUI, React, jsx, fragment }) => {
    const { But<PERSON> } = ReactPCUI;
    return fragment(
        jsx(<PERSON><PERSON>, {
            text: 'Custom Shader',
            onClick: () => {
                observer.set('shader', !observer.get('shader'));
            }
        })
    );
};
