// @config HIDDEN
import { data } from 'examples/observer';
import { deviceType, rootPath } from 'examples/utils';
import * as pc from 'playcanvas';

const canvas = /** @type {HTMLCanvasElement} */ (document.getElementById('application-canvas'));
window.focus();

const assets = {
    fly: new pc.Asset('fly', 'script', { url: `${rootPath}/static/scripts/camera/fly-camera.js` }),
    helipad: new pc.Asset(
        'helipad-env-atlas',
        'texture',
        { url: `${rootPath}/static/assets/cubemaps/morning-env-atlas.png` },
        { type: pc.TEXTURETYPE_RGBP, mipmaps: false }
    ),
    normal: new pc.Asset('normal', 'texture', { url: `${rootPath}/static/assets/textures/seaside-rocks01-normal.jpg` }),
    height: new pc.Asset('height', 'texture', { url: `${rootPath}/static/assets/textures/seaside-rocks01-height.jpg` }),
    diffuse: new pc.Asset('diffuse', 'texture', { url: `${rootPath}/static/assets/textures/seaside-rocks01-color.jpg` })
};

const gfxOptions = {
    deviceTypes: [deviceType],
    glslangUrl: `${rootPath}/static/lib/glslang/glslang.js`,
    twgslUrl: `${rootPath}/static/lib/twgsl/twgsl.js`
};

const device = await pc.createGraphicsDevice(canvas, gfxOptions);
device.maxPixelRatio = Math.min(window.devicePixelRatio, 2);

const createOptions = new pc.AppOptions();
createOptions.graphicsDevice = device;
createOptions.mouse = new pc.Mouse(document.body);
createOptions.touch = new pc.TouchDevice(document.body);
createOptions.keyboard = new pc.Keyboard(document.body);

createOptions.componentSystems = [pc.RenderComponentSystem, pc.CameraComponentSystem, pc.LightComponentSystem, pc.ScriptComponentSystem];
createOptions.resourceHandlers = [pc.TextureHandler, pc.ScriptHandler];

const app = new pc.AppBase(canvas);
app.init(createOptions);

// Set the canvas to fill the window and automatically change resolution to be the same as the canvas size
app.setCanvasFillMode(pc.FILLMODE_FILL_WINDOW);
app.setCanvasResolution(pc.RESOLUTION_AUTO);

// Ensure canvas is resized when window changes size
const resize = () => app.resizeCanvas();
window.addEventListener('resize', resize);
app.on('destroy', () => {
    window.removeEventListener('resize', resize);
});

const assetListLoader = new pc.AssetListLoader(Object.values(assets), app.assets);
assetListLoader.load(() => {
    app.start();

    app.scene.envAtlas = assets.helipad.resource;
    app.scene.exposure = 1;

    // Create an entity with a camera component
    const camera = new pc.Entity();
    camera.addComponent('camera', {
        toneMapping: pc.TONEMAP_ACES,
        fov: 75
    });
    camera.translate(0, 0, 3);
    app.root.addChild(camera);

    // add fly camera script
    camera.addComponent('script');
    camera.script.create('flyCamera', {
        attributes: {
            speed: 100
        }
    });

    // Create an entity with an omni light component
    const light = new pc.Entity();
    light.addComponent('light', {
        type: 'omni',
        color: new pc.Color(1, 1, 1),
        intensity: 2,
        castShadows: false,
        range: 800
    });
    light.addComponent('render', {
        type: 'sphere'
    });
    light.setLocalScale(30, 30, 30);
    light.setLocalPosition(200, -100, 0);
    app.root.addChild(light);

    // material with parallax mapping
    const tiling = 3;
    const parallaxMaterial = new pc.StandardMaterial();
    parallaxMaterial.diffuseMap = assets.diffuse.resource;
    parallaxMaterial.normalMap = assets.normal.resource;
    parallaxMaterial.heightMap = assets.height.resource;
    parallaxMaterial.gloss = 0.3;
    parallaxMaterial.useMetalness = true;
    parallaxMaterial.diffuseMapTiling.set(tiling, tiling);
    parallaxMaterial.normalMapTiling.set(tiling, tiling);
    parallaxMaterial.heightMapTiling.set(tiling, tiling);
    parallaxMaterial.update();

    /**
     * Helper function to create a 3d primitive including its material.
     *
     * @param {string} primitiveType - The primitive type.
     * @param {pc.Vec3} position - The position.
     * @param {pc.Vec3} scale - The scale.
     * @param {pc.Material} material - The material.
     */
    function createPrimitive(primitiveType, position, scale, material) {
        // create the primitive using the material
        const primitive = new pc.Entity();
        primitive.addComponent('render', {
            type: primitiveType,
            material: material,
            castShadows: false,
            receiveShadows: false
        });

        // set position and scale and add it to scene
        primitive.setLocalPosition(position);
        primitive.setLocalScale(scale);
        app.root.addChild(primitive);
    }

    // create the ground plane from the boxes
    createPrimitive('box', new pc.Vec3(0, -200, 0), new pc.Vec3(800, 2, 800), parallaxMaterial);
    createPrimitive('box', new pc.Vec3(0, 200, 0), new pc.Vec3(800, 2, 800), parallaxMaterial);

    // walls
    createPrimitive('box', new pc.Vec3(400, 0, 0), new pc.Vec3(2, 400, 800), parallaxMaterial);
    createPrimitive('box', new pc.Vec3(-400, 0, 0), new pc.Vec3(2, 400, 800), parallaxMaterial);
    createPrimitive('box', new pc.Vec3(0, 0, -400), new pc.Vec3(800, 400, 0), parallaxMaterial);
    createPrimitive('box', new pc.Vec3(0, 0, 400), new pc.Vec3(800, 400, 0), parallaxMaterial);

    // initial values
    data.set('data', {
        height: 0.1
    });

    // update things each frame
    app.on('update', (dt) => {
        const height = data.get('data.height');
        if (height !== parallaxMaterial.heightMapFactor) {
            parallaxMaterial.heightMapFactor = height;
            parallaxMaterial.update();
        }
    });
});

export { app };
