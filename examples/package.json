{"name": "examples-browser", "version": "0.0.0", "description": "Examples browser for the PlayCanvas Engine", "main": "index.js", "type": "module", "scripts": {"build": "npm run -s build:metadata && cross-env NODE_ENV=production rollup -c", "build:metadata": "node ./scripts/build-metadata.mjs", "build:thumbnails": "node ./scripts/build-thumbnails.mjs", "clean": "node ./scripts/clean.mjs", "develop": "cross-env NODE_ENV=development concurrently --kill-others \"npm run watch\" \"npm run serve\"", "lint": "eslint .", "serve": "serve dist -l 5555 --no-request-logging --config ../serve.json", "watch": "npm run -s build:metadata && cross-env NODE_ENV=development rollup -c -w"}, "devDependencies": {"@babel/standalone": "7.26.9", "@monaco-editor/react": "4.7.0", "@playcanvas/eslint-config": "2.0.9", "@playcanvas/observer": "1.6.6", "@playcanvas/pcui": "4.6.0", "@rollup/plugin-commonjs": "28.0.3", "@rollup/plugin-node-resolve": "15.3.1", "@rollup/plugin-replace": "6.0.2", "@rollup/plugin-terser": "0.4.4", "@tweenjs/tween.js": "25.0.0", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@types/react-router-dom": "5.3.3", "concurrently": "9.1.2", "cross-env": "7.0.3", "eslint": "9.22.0", "examples": "file:./iframe", "fflate": "0.8.2", "fs-extra": "11.3.0", "monaco-editor": "0.33.0", "playcanvas": "file:..", "prop-types": "15.8.1", "puppeteer": "23.11.1", "react": "18.3.1", "react-dom": "18.3.1", "react-es6": "1.0.2", "react-router-dom": "5.3.4", "rollup": "4.35.0", "serve": "14.2.4", "sharp": "0.33.5"}, "author": "PlayCanvas <<EMAIL>>", "license": "MIT"}