{"skeleton": {"hash": "Gqe4cxN6nMuC7/4IxjgEs6+PN0o", "spine": "3.8.84", "x": -190.66, "y": -8, "width": 419.84, "height": 686.08, "images": "./images/", "audio": ""}, "bones": [{"name": "root", "rotation": 0.28}, {"name": "hip", "parent": "root", "y": 247.27}, {"name": "crosshair", "parent": "root", "x": 302.83, "y": 569.45, "color": "ff3f00ff"}, {"name": "aim-constraint-target", "parent": "hip", "length": 26.24, "rotation": 19.61, "x": 1.02, "y": 5.62, "color": "abe323ff"}, {"name": "rear-foot-target", "parent": "root", "x": 61.91, "y": 0.42, "color": "ff3f00ff"}, {"name": "rear-leg-target", "parent": "rear-foot-target", "x": -33.91, "y": 37.34, "color": "ff3f00ff"}, {"name": "rear-thigh", "parent": "hip", "length": 85.72, "rotation": -72.54, "x": 8.91, "y": -5.63, "color": "ff000dff"}, {"name": "rear-shin", "parent": "rear-thigh", "length": 121.88, "rotation": -19.83, "x": 86.1, "y": -1.33, "color": "ff000dff"}, {"name": "rear-foot", "parent": "rear-shin", "length": 51.58, "rotation": 45.78, "x": 121.46, "y": -0.76, "color": "ff000dff"}, {"name": "back-foot-tip", "parent": "rear-foot", "length": 50.3, "rotation": -0.85, "x": 51.17, "y": 0.24, "transform": "noRotationOrReflection", "color": "ff000dff"}, {"name": "board-ik", "parent": "root", "x": -131.78, "y": 69.09, "color": "4c56ffff"}, {"name": "clipping", "parent": "root"}, {"name": "hoverboard-controller", "parent": "root", "rotation": -0.28, "x": -329.69, "y": 69.82, "color": "ff0004ff"}, {"name": "exhaust1", "parent": "hoverboard-controller", "rotation": 3.02, "x": -249.68, "y": 53.39}, {"name": "exhaust2", "parent": "hoverboard-controller", "rotation": 26.34, "x": -191.6, "y": -22.92}, {"name": "exhaust3", "parent": "hoverboard-controller", "rotation": -12.34, "x": -236.03, "y": 80.54, "scaleX": 0.7847, "scaleY": 0.7847}, {"name": "portal-root", "parent": "root", "x": 12.9, "y": 328.54, "scaleX": 2.0334, "scaleY": 2.0334}, {"name": "flare1", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare10", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare2", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare3", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare4", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare5", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare6", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare7", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare8", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "flare9", "parent": "portal-root", "x": -6.34, "y": -161.57}, {"name": "torso", "parent": "hip", "length": 42.52, "rotation": 103.82, "x": -1.62, "y": 4.9, "color": "e0da19ff"}, {"name": "torso2", "parent": "torso", "length": 42.52, "x": 42.52, "color": "e0da19ff"}, {"name": "torso3", "parent": "torso2", "length": 42.52, "x": 42.52, "color": "e0da19ff"}, {"name": "front-upper-arm", "parent": "torso3", "length": 69.45, "rotation": 168.38, "x": 18.72, "y": 19.33, "color": "00ff04ff"}, {"name": "front-bracer", "parent": "front-upper-arm", "length": 40.57, "rotation": 18.3, "x": 68.8, "y": -0.68, "color": "00ff04ff"}, {"name": "front-fist", "parent": "front-bracer", "length": 65.39, "rotation": 12.43, "x": 40.57, "y": 0.2, "color": "00ff04ff"}, {"name": "front-foot-target", "parent": "root", "x": -13.53, "y": 0.04, "color": "ff3f00ff"}, {"name": "front-leg-target", "parent": "front-foot-target", "x": -28.4, "y": 29.06, "color": "ff3f00ff"}, {"name": "front-thigh", "parent": "hip", "length": 74.81, "rotation": -95.51, "x": -17.46, "y": -11.64, "color": "00ff04ff"}, {"name": "front-shin", "parent": "front-thigh", "length": 128.77, "rotation": -2.21, "x": 78.69, "y": 1.6, "color": "00ff04ff"}, {"name": "front-foot", "parent": "front-shin", "length": 41.01, "rotation": 51.27, "x": 128.76, "y": -0.34, "color": "00ff04ff"}, {"name": "front-foot-tip", "parent": "front-foot", "length": 56.03, "rotation": -1.68, "x": 41.42, "y": -0.09, "transform": "noRotationOrReflection", "color": "00ff04ff"}, {"name": "rear-upper-arm", "parent": "torso3", "length": 51.94, "rotation": -169.56, "x": 7.32, "y": -19.22, "color": "ff000dff"}, {"name": "rear-bracer", "parent": "rear-upper-arm", "length": 34.56, "rotation": 23.15, "x": 51.36, "color": "ff000dff"}, {"name": "gun", "parent": "rear-bracer", "length": 43.11, "rotation": -5.43, "x": 34.42, "y": -0.45, "color": "ff000dff"}, {"name": "gun-tip", "parent": "gun", "rotation": 7.1, "x": 200.78, "y": 52.5, "color": "ff0000ff"}, {"name": "neck", "parent": "torso3", "length": 25.45, "rotation": -31.54, "x": 42.46, "y": -0.31, "color": "e0da19ff"}, {"name": "head", "parent": "neck", "length": 131.79, "rotation": 26.1, "x": 27.66, "y": -0.26, "color": "e0da19ff"}, {"name": "hair1", "parent": "head", "length": 47.23, "rotation": -49.1, "x": 149.83, "y": -59.77, "color": "e0da19ff"}, {"name": "hair2", "parent": "hair1", "length": 55.57, "rotation": 50.42, "x": 47.23, "y": 0.19, "color": "e0da19ff"}, {"name": "hair3", "parent": "head", "length": 62.22, "rotation": -32.17, "x": 164.14, "y": 3.68, "color": "e0da19ff"}, {"name": "hair4", "parent": "hair3", "length": 80.28, "rotation": 83.71, "x": 62.22, "y": -0.04, "color": "e0da19ff"}, {"name": "hoverboard-thruster-front", "parent": "hoverboard-controller", "rotation": -29.2, "x": 95.77, "y": -2.99, "transform": "noRotationOrReflection"}, {"name": "hoverboard-thruster-rear", "parent": "hoverboard-controller", "rotation": -29.2, "x": -76.47, "y": -4.88, "transform": "noRotationOrReflection"}, {"name": "hoverglow-front", "parent": "hoverboard-thruster-front", "rotation": 0.17, "x": -1.78, "y": -37.79}, {"name": "hoverglow-rear", "parent": "hoverboard-thruster-rear", "rotation": 0.17, "x": 1.06, "y": -35.66}, {"name": "muzzle", "parent": "rear-bracer", "rotation": 3.06, "x": 242.34, "y": 34.26, "color": "ffb900ff"}, {"name": "muzzle-ring", "parent": "muzzle", "color": "ffb900ff"}, {"name": "muzzle-ring2", "parent": "muzzle", "color": "ffb900ff"}, {"name": "muzzle-ring3", "parent": "muzzle", "color": "ffb900ff"}, {"name": "muzzle-ring4", "parent": "muzzle", "color": "ffb900ff"}, {"name": "portal", "parent": "portal-root"}, {"name": "portal-shade", "parent": "portal-root"}, {"name": "portal-streaks1", "parent": "portal-root"}, {"name": "portal-streaks2", "parent": "portal-root"}, {"name": "side-glow1", "parent": "hoverboard-controller", "x": -110.56, "y": 2.62, "color": "000effff"}, {"name": "side-glow2", "parent": "hoverboard-controller", "x": -110.56, "y": 2.62, "scaleX": 0.738, "scaleY": 0.738, "color": "000effff"}], "slots": [{"name": "portal-bg", "bone": "portal"}, {"name": "portal-shade", "bone": "portal-shade"}, {"name": "portal-streaks2", "bone": "portal-streaks2", "blend": "additive"}, {"name": "portal-streaks1", "bone": "portal-streaks1", "blend": "additive"}, {"name": "portal-flare8", "bone": "flare8", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare9", "bone": "flare9", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare10", "bone": "flare10", "color": "c3cbffff", "blend": "additive"}, {"name": "clipping", "bone": "clipping"}, {"name": "exhaust3", "bone": "exhaust3", "color": "5eb4ffff", "blend": "additive"}, {"name": "hoverboard-thruster-rear", "bone": "hoverboard-thruster-rear"}, {"name": "hoverboard-thruster-front", "bone": "hoverboard-thruster-front"}, {"name": "hoverboard-board", "bone": "hoverboard-controller"}, {"name": "side-glow1", "bone": "side-glow1", "color": "ff8686ff", "blend": "additive"}, {"name": "side-glow3", "bone": "side-glow1", "color": "ff8686ff", "blend": "additive"}, {"name": "side-glow2", "bone": "side-glow2", "color": "ff8686ff", "blend": "additive"}, {"name": "hoverglow-front", "bone": "hoverglow-front", "color": "5eb4ffff", "blend": "additive"}, {"name": "hoverglow-rear", "bone": "hoverglow-rear", "color": "5eb4ffff", "blend": "additive"}, {"name": "exhaust1", "bone": "exhaust2", "color": "5eb4ffff", "blend": "additive"}, {"name": "exhaust2", "bone": "exhaust1", "color": "5eb4ffff", "blend": "additive"}, {"name": "rear-upper-arm", "bone": "rear-upper-arm", "attachment": "rear-upper-arm"}, {"name": "rear-bracer", "bone": "rear-bracer", "attachment": "rear-bracer"}, {"name": "gun", "bone": "gun", "attachment": "gun"}, {"name": "rear-foot", "bone": "rear-foot", "attachment": "rear-foot"}, {"name": "rear-thigh", "bone": "rear-thigh", "attachment": "rear-thigh"}, {"name": "rear-shin", "bone": "rear-shin", "attachment": "rear-shin"}, {"name": "neck", "bone": "neck", "attachment": "neck"}, {"name": "torso", "bone": "torso", "attachment": "torso"}, {"name": "front-upper-arm", "bone": "front-upper-arm", "attachment": "front-upper-arm"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye", "bone": "head", "attachment": "eye-indifferent"}, {"name": "front-thigh", "bone": "front-thigh", "attachment": "front-thigh"}, {"name": "front-foot", "bone": "front-foot", "attachment": "front-foot"}, {"name": "front-shin", "bone": "front-shin", "attachment": "front-shin"}, {"name": "mouth", "bone": "head", "attachment": "mouth-smile"}, {"name": "goggles", "bone": "head", "attachment": "goggles"}, {"name": "front-bracer", "bone": "front-bracer", "attachment": "front-bracer"}, {"name": "front-fist", "bone": "front-fist", "attachment": "front-fist-closed"}, {"name": "muzzle", "bone": "muzzle"}, {"name": "head-bb", "bone": "head"}, {"name": "portal-flare1", "bone": "flare1", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare2", "bone": "flare2", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare3", "bone": "flare3", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare4", "bone": "flare4", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare5", "bone": "flare5", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare6", "bone": "flare6", "color": "c3cbffff", "blend": "additive"}, {"name": "portal-flare7", "bone": "flare7", "color": "c3cbffff", "blend": "additive"}, {"name": "crosshair", "bone": "crosshair"}, {"name": "muzzle-glow", "bone": "gun-tip", "color": "ffffff00", "blend": "additive"}, {"name": "muzzle-ring", "bone": "muzzle-ring", "color": "d8baffff", "blend": "additive"}, {"name": "muzzle-ring2", "bone": "muzzle-ring2", "color": "d8baffff", "blend": "additive"}, {"name": "muzzle-ring3", "bone": "muzzle-ring3", "color": "d8baffff", "blend": "additive"}, {"name": "muzzle-ring4", "bone": "muzzle-ring4", "color": "d8baffff", "blend": "additive"}], "ik": [{"name": "aim-ik", "order": 12, "bones": ["rear-upper-arm"], "target": "crosshair", "mix": 0}, {"name": "aim-torso-ik", "order": 7, "bones": ["aim-constraint-target"], "target": "crosshair"}, {"name": "board-ik", "bones": ["hoverboard-controller"], "target": "board-ik"}, {"name": "front-foot-ik", "order": 5, "bones": ["front-foot"], "target": "front-foot-target"}, {"name": "front-leg-ik", "order": 3, "bones": ["front-thigh", "front-shin"], "target": "front-leg-target", "bendPositive": false}, {"name": "rear-foot-ik", "order": 6, "bones": ["rear-foot"], "target": "rear-foot-target"}, {"name": "rear-leg-ik", "order": 4, "bones": ["rear-thigh", "rear-shin"], "target": "rear-leg-target", "bendPositive": false}], "transform": [{"name": "aim-front-arm-transform", "order": 10, "bones": ["front-upper-arm"], "target": "aim-constraint-target", "rotation": -180, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "aim-head-transform", "order": 9, "bones": ["head"], "target": "aim-constraint-target", "rotation": 84.3, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "aim-rear-arm-transform", "order": 11, "bones": ["rear-upper-arm"], "target": "aim-constraint-target", "x": 57.7, "y": 56.4, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "aim-torso-transform", "order": 8, "bones": ["torso"], "target": "aim-constraint-target", "rotation": 69.5, "shearY": -36, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "front-foot-board-transform", "order": 1, "bones": ["front-foot-target"], "target": "hoverboard-controller", "x": -69.8, "y": 20.7, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "rear-foot-board-transform", "order": 2, "bones": ["rear-foot-target"], "target": "hoverboard-controller", "x": 86.6, "y": 21.3, "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "toes-board", "order": 13, "bones": ["front-foot-tip", "back-foot-tip"], "target": "hoverboard-controller", "rotateMix": 0, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"clipping": {"clipping": {"type": "clipping", "end": "head-bb", "vertexCount": 9, "vertices": [66.76, 509.48, 19.98, 434.54, 5.34, 336.28, 22.19, 247.93, 77.98, 159.54, 182.21, -97.56, 1452.26, -99.8, 1454.33, 843.61, 166.57, 841.02], "color": "ce3a3aff"}}, "crosshair": {"crosshair": {"width": 89, "height": 89}}, "exhaust1": {"hoverglow-small": {"scaleX": 0.4629, "scaleY": 0.8129, "rotation": -83.07, "width": 274, "height": 75}}, "exhaust2": {"hoverglow-small": {"x": 0.01, "y": -0.76, "scaleX": 0.4208, "scaleY": 0.8403, "rotation": -89.25, "width": 274, "height": 75}}, "exhaust3": {"hoverglow-small": {"scaleX": 0.4629, "scaleY": 0.8129, "rotation": -83.07, "width": 274, "height": 75}}, "eye": {"eye-indifferent": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [73.41, -91.35, 23.16, -13.11, 98.03, 34.99, 148.28, -43.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 89}, "eye-surprised": {"x": 85.72, "y": -28.18, "rotation": -70.63, "width": 93, "height": 89}}, "front-bracer": {"front-bracer": {"x": 12.03, "y": -1.68, "rotation": 79.6, "width": 58, "height": 80}}, "front-fist": {"front-fist-closed": {"x": 35.5, "y": 6, "rotation": 67.16, "width": 75, "height": 82}, "front-fist-open": {"x": 39.57, "y": 7.76, "rotation": 67.16, "width": 86, "height": 87}}, "front-foot": {"front-foot": {"type": "mesh", "uvs": [0.59417, 0.23422, 0.62257, 0.30336, 0.6501, 0.37036, 0.67637, 0.38404, 0.72068, 0.4071, 0.76264, 0.42894, 1, 0.70375, 1, 1, 0.65517, 1, 0.46923, 0.99999, 0, 1, 0, 0.39197, 0.17846, 0, 0.49796, 0], "triangles": [8, 9, 3, 4, 8, 3, 5, 8, 4, 6, 8, 5, 8, 6, 7, 11, 1, 10, 0, 12, 13, 0, 11, 12, 0, 1, 11, 9, 2, 3, 1, 2, 10, 9, 10, 2], "vertices": [2, 37, 18.17, 41.57, 0.7896, 38, 12.46, 46.05, 0.2104, 2, 37, 24.08, 40.76, 0.71228, 38, 16.12, 41.34, 0.28772, 2, 37, 29.81, 39.98, 0.55344, 38, 19.67, 36.78, 0.44656, 2, 37, 32.81, 41.67, 0.38554, 38, 23, 35.89, 0.61446, 2, 37, 37.86, 44.52, 0.25567, 38, 28.61, 34.4, 0.74433, 2, 37, 42.65, 47.22, 0.17384, 38, 33.92, 32.99, 0.82616, 1, 38, 64.15, 14.56, 1, 1, 38, 64.51, -5.87, 1, 1, 38, 21.08, -6.64, 1, 2, 37, 44.67, -6.77, 0.5684, 38, -2.34, -6.97, 0.4316, 1, 37, 3.1, -48.81, 1, 1, 37, -26.73, -19.31, 1, 1, 37, -30.15, 15.69, 1, 1, 37, -1.84, 44.32, 1], "hull": 14, "edges": [14, 16, 16, 18, 18, 20, 4, 18, 20, 22, 24, 26, 22, 24, 12, 14, 10, 12, 2, 4, 2, 20, 4, 6, 6, 16, 2, 0, 0, 26, 6, 8, 8, 10], "width": 126, "height": 69}}, "front-shin": {"front-shin": {"type": "mesh", "uvs": [0.90031, 0.05785, 1, 0.12828, 1, 0.21619, 0.9025, 0.31002, 0.78736, 0.35684, 0.78081, 0.39874, 0.77215, 0.45415, 0.77098, 0.51572, 0.84094, 0.63751, 0.93095, 0.7491, 0.95531, 0.7793, 0.78126, 0.87679, 0.5613, 1, 0.2687, 1, 0, 1, 0.00279, 0.96112, 0.01358, 0.81038, 0.02822, 0.60605, 0.08324, 0.45142, 0.18908, 0.31882, 0.29577, 0.2398, 0.30236, 0.14941, 0.37875, 0.05902, 0.53284, 0, 0.70538, 0, 0.41094, 0.71968, 0.40743, 0.54751, 0.41094, 0.4536, 0.4724, 0.35186, 0.33367, 0.27829, 0.50226, 0.31664, 0.65328, 0.67507, 0.60762, 0.52716, 0.6006, 0.45125, 0.62747, 0.37543, 0.6573, 0.3385, 0.27843, 0.32924, 0.18967, 0.45203, 0.16509, 0.58586, 0.18265, 0.7682, 0.50532, 0.24634, 0.59473, 0.17967, 0.60161, 0.10611, 0.51392, 0.04327, 0.72198, 0.28849, 0.82343, 0.20266, 0.86814, 0.11377, 0.79592, 0.04634, 0.44858, 0.15515, 0.25466, 0.96219, 0.53169, 0.9448, 0.7531, 0.8324], "triangles": [24, 0, 47, 43, 23, 24, 47, 43, 24, 43, 22, 23, 42, 43, 47, 46, 47, 0, 42, 47, 46, 46, 0, 1, 48, 22, 43, 48, 43, 42, 21, 22, 48, 41, 48, 42, 45, 42, 46, 41, 42, 45, 46, 1, 2, 45, 46, 2, 40, 48, 41, 48, 20, 21, 29, 48, 40, 29, 20, 48, 44, 41, 45, 40, 41, 44, 3, 45, 2, 44, 45, 3, 30, 29, 40, 35, 30, 40, 36, 19, 20, 36, 20, 29, 44, 35, 40, 28, 29, 30, 4, 44, 3, 35, 44, 4, 34, 30, 35, 5, 35, 4, 34, 28, 30, 33, 28, 34, 37, 19, 36, 18, 19, 37, 27, 29, 28, 27, 28, 33, 36, 29, 27, 37, 36, 27, 5, 34, 35, 6, 34, 5, 33, 34, 6, 6, 32, 33, 7, 32, 6, 26, 37, 27, 38, 18, 37, 38, 37, 26, 17, 18, 38, 31, 32, 7, 31, 7, 8, 32, 25, 26, 38, 26, 25, 27, 33, 32, 32, 26, 27, 39, 38, 25, 17, 38, 39, 16, 17, 39, 51, 31, 8, 51, 8, 9, 11, 51, 9, 11, 9, 10, 31, 50, 25, 31, 25, 32, 50, 31, 51, 49, 39, 25, 49, 25, 50, 15, 16, 39, 49, 15, 39, 13, 49, 50, 14, 15, 49, 13, 14, 49, 12, 50, 51, 12, 51, 11, 13, 50, 12], "vertices": [-23.66, 19.37, -11.73, 28.98, 4.34, 30.83, 22.41, 24.87, 32.05, 16.48, 39.77, 16.83, 49.98, 17.3, 61.25, 18.5, 82.85, 26.78, 102.4, 36.46, 107.69, 39.09, 127.15, 26.97, 151.74, 11.65, 154.49, -12.18, 157.02, -34.07, 149.89, -34.66, 122.23, -36.97, 84.75, -40.09, 55.97, -38.88, 30.73, -33.05, 15.29, -26.03, -1.3, -27.41, -18.54, -23.09, -30.78, -11.79, -32.4, 2.27, 101.92, -6.52, 70.48, -10.44, 53.28, -12.14, 34.11, -9.28, 21.96, -22.13, 27.39, -7.59, 91.48, 12.28, 64.88, 5.44, 51.07, 3.26, 36.95, 3.85, 29.92, 5.5, 31.8, -25.56, 55.08, -30.19, 79.77, -29.37, 112.93, -24.09, 14.51, -8.83, 1.48, -2.95, -12.03, -3.94, -22.69, -12.41, 20.17, 9.71, 3.53, 16.16, -13.14, 17.93, -24.78, 10.62, -1.62, -15.37, 147.71, -14.13, 141.93, 8.07, 119.3, 23.74], "hull": 25, "edges": [8, 6, 6, 4, 4, 2, 2, 0, 0, 48, 46, 48, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 32, 34, 50, 52, 52, 54, 54, 56, 40, 58, 58, 60, 8, 10, 20, 22, 22, 24, 62, 64, 64, 66, 66, 68, 8, 70, 70, 60, 68, 70, 58, 72, 72, 74, 74, 76, 76, 78, 24, 26, 26, 28, 58, 80, 80, 82, 82, 84, 84, 86, 86, 44, 70, 88, 88, 90, 90, 92, 92, 94, 94, 48, 80, 88, 88, 6, 82, 90, 90, 4, 84, 92, 92, 2, 86, 94, 94, 0, 56, 60, 10, 12, 12, 14, 14, 16, 28, 30, 30, 32, 26, 98, 98, 78, 30, 98, 24, 100, 100, 50, 98, 100, 22, 102, 102, 62, 100, 102, 16, 18, 18, 20, 102, 18], "width": 82, "height": 184}}, "front-thigh": {"front-thigh": {"x": 42.48, "y": 4.45, "rotation": 84.87, "width": 45, "height": 112}}, "front-upper-arm": {"front-upper-arm": {"x": 28.31, "y": 7.37, "rotation": 97.9, "width": 46, "height": 97}}, "goggles": {"goggles": {"type": "mesh", "uvs": [0.53653, 0.04114, 0.72922, 0.16036, 0.91667, 0.33223, 0.97046, 0.31329, 1, 0.48053, 0.95756, 0.5733, 0.88825, 0.6328, 0.86878, 0.78962, 0.77404, 0.8675, 0.72628, 1, 0.60714, 0.93863, 0.49601, 0.88138, 0.41558, 0.75027, 0.32547, 0.70084, 0.2782, 0.58257, 0.1721, 0.63281, 0.17229, 0.75071, 0.10781, 0.79898, 0, 0.32304, 0, 0.12476, 0.07373, 0.07344, 0.15423, 0.10734, 0.23165, 0.13994, 0.30313, 0.02256, 0.34802, 0, 0.42979, 0.69183, 0.39476, 0.51042, 0.39488, 0.31512, 0.45878, 0.23198, 0.56501, 0.28109, 0.69961, 0.39216, 0.82039, 0.54204, 0.85738, 0.62343, 0.91107, 0.51407, 0.72639, 0.32147, 0.58764, 0.19609, 0.48075, 0.11269, 0.37823, 0.05501, 0.3287, 0.17866, 0.319, 0.305, 0.36036, 0.53799, 0.40327, 0.70072, 0.30059, 0.55838, 0.21957, 0.2815, 0.09963, 0.28943, 0.56863, 0.4368, 0.4911, 0.37156, 0.51185, 0.52093, 0.67018, 0.59304, 0.7619, 0.68575, 0.73296, 0.43355], "triangles": [49, 8, 48, 9, 48, 8, 12, 25, 11, 48, 9, 10, 47, 48, 10, 47, 10, 25, 25, 10, 11, 8, 49, 7, 17, 15, 16, 17, 18, 15, 49, 32, 7, 7, 32, 6, 41, 42, 40, 12, 41, 25, 41, 12, 42, 13, 14, 42, 12, 13, 42, 41, 40, 25, 40, 26, 25, 25, 26, 47, 49, 31, 32, 31, 49, 50, 18, 44, 15, 42, 14, 44, 14, 15, 44, 5, 6, 33, 6, 32, 33, 32, 31, 33, 47, 45, 48, 49, 48, 50, 50, 45, 30, 50, 48, 45, 42, 44, 43, 5, 33, 4, 42, 39, 40, 42, 43, 39, 31, 50, 33, 40, 39, 26, 45, 47, 46, 33, 2, 4, 2, 33, 34, 47, 26, 46, 26, 27, 46, 26, 39, 27, 2, 3, 4, 30, 45, 29, 30, 34, 50, 33, 50, 34, 45, 46, 29, 30, 29, 34, 27, 28, 46, 46, 28, 29, 18, 19, 44, 29, 35, 34, 2, 34, 1, 34, 35, 1, 28, 27, 38, 27, 39, 38, 39, 43, 38, 44, 19, 21, 44, 21, 43, 21, 19, 20, 43, 22, 38, 43, 21, 22, 29, 28, 35, 28, 36, 35, 28, 38, 36, 36, 0, 35, 35, 0, 1, 22, 23, 38, 38, 37, 36, 37, 23, 24, 37, 38, 23, 36, 37, 0, 37, 24, 0], "vertices": [172.09, 22.81, 170.1, -31.19, 159.41, -86.8, 167.03, -99.01, 143.4, -115.48, 125.21, -110.14, 109.89, -96.35, 83.65, -100.19, 63.25, -81.16, 38.37, -76.69, 37.67, -43.98, 37.01, -13.47, 50.58, 13.55, 50.52, 38.45, 64.95, 56.6, 47.9, 79.96, 29.45, 73.42, 16.31, 86.64, 81.51, 139.38, 112.56, 150.3, 126.97, 134.97, 128.63, 113.28, 130.23, 92.43, 154.79, 81.29, 162.21, 71.48, 60.96, 13.27, 86.33, 31.88, 116.93, 42.6, 135.47, 31.44, 136.98, 2.59, 131.23, -36.66, 118.22, -74.65, 108.69, -88.24, 130.46, -95.44, 144.63, -39.36, 152.25, 1.7, 156.06, 32.6, 156.22, 61.02, 132.57, 66.41, 111.94, 61.84, 79.04, 38.83, 57.27, 19.31, 70.67, 52.42, 107.02, 87.61, 95.4, 116.7, 112.91, -6.87, 116.42, 15.8, 94.82, 2.47, 97.24, -40.48, 90.66, -68.16, 127.65, -47.15], "hull": 25, "edges": [36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 48, 48, 46, 46, 44, 36, 38, 40, 38, 24, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 12, 8, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 24, 24, 84, 84, 86, 86, 44, 40, 42, 42, 44, 42, 88, 88, 30, 58, 90, 90, 92, 92, 94, 18, 20, 20, 22, 94, 20, 18, 96, 96, 98, 60, 100, 100, 62, 98, 100], "width": 261, "height": 166}}, "gun": {"gun": {"x": 77.3, "y": 16.4, "rotation": 60.83, "width": 210, "height": 203}}, "head": {"head": {"type": "mesh", "uvs": [0.75919, 0.06107, 0.88392, 0.17893, 0.90174, 0.30856, 0.94224, 0.1966, 1, 0.26584, 1, 0.422, 0.95864, 0.46993, 0.92118, 0.51333, 0.85957, 0.5347, 0.78388, 0.65605, 0.74384, 0.74838, 0.85116, 0.75151, 0.84828, 0.82564, 0.81781, 0.85367, 0.75599, 0.85906, 0.76237, 0.90468, 0.65875, 1, 0.38337, 1, 0.1858, 0.85404, 0.12742, 0.81091, 0.06025, 0.69209, 0, 0.58552, 0, 0.41021, 0.0853, 0.20692, 0.24243, 0.14504, 0.5, 0.1421, 0.50324, 0.07433, 0.41738, 0, 0.57614, 0, 0.85059, 0.36087, 0.73431, 0.43206, 0.68481, 0.31271, 0.72165, 0.16718, 0.55931, 0.04154, 0.44764, 0.22895, 0.23926, 0.26559, 0.71272, 0.44036, 0.56993, 0.383, 0.41678, 0.33511, 0.293, 0.31497, 0.70802, 0.44502, 0.56676, 0.38976, 0.41521, 0.34416, 0.28754, 0.33017, 0.88988, 0.50177, 0.30389, 0.73463, 0.2646, 0.65675, 0.21414, 0.61584, 0.14613, 0.62194, 0.10316, 0.66636, 0.10358, 0.72557, 0.14505, 0.79164, 0.20263, 0.81355, 0.27873, 0.80159, 0.34947, 0.7376, 0.23073, 0.57073, 0.08878, 0.60707, 0.29461, 0.8129, 0.73006, 0.87883, 0.69805, 0.87348, 0.66166, 0.79681], "triangles": [34, 25, 31, 37, 38, 34, 31, 32, 29, 31, 37, 34, 37, 41, 38, 30, 31, 29, 36, 37, 31, 33, 27, 28, 26, 27, 33, 0, 33, 28, 32, 33, 0, 32, 0, 1, 33, 25, 26, 33, 32, 25, 31, 25, 32, 2, 32, 1, 2, 3, 4, 2, 29, 32, 2, 4, 5, 29, 2, 5, 6, 29, 5, 30, 36, 31, 30, 29, 6, 44, 30, 6, 36, 30, 44, 34, 24, 25, 35, 23, 24, 35, 24, 34, 39, 35, 34, 39, 22, 35, 38, 39, 34, 42, 39, 38, 43, 39, 42, 41, 42, 38, 22, 23, 35, 43, 22, 39, 40, 37, 36, 41, 37, 40, 7, 44, 6, 8, 36, 44, 40, 36, 8, 8, 44, 7, 55, 22, 43, 56, 21, 22, 55, 56, 22, 55, 48, 56, 47, 48, 55, 9, 40, 8, 55, 54, 46, 42, 55, 43, 47, 55, 46, 49, 56, 48, 20, 21, 56, 20, 56, 49, 50, 49, 48, 20, 49, 50, 46, 54, 45, 54, 55, 41, 55, 42, 41, 9, 60, 40, 46, 51, 50, 60, 41, 40, 10, 60, 9, 54, 41, 60, 46, 52, 51, 19, 50, 51, 50, 48, 47, 47, 46, 50, 46, 45, 52, 20, 50, 19, 57, 53, 45, 57, 45, 54, 53, 52, 45, 12, 10, 11, 13, 10, 12, 18, 51, 52, 19, 51, 18, 18, 52, 53, 18, 53, 57, 14, 10, 13, 60, 10, 14, 59, 60, 14, 58, 59, 14, 58, 14, 15, 17, 54, 60, 16, 17, 60, 57, 54, 17, 18, 57, 17, 59, 16, 60, 16, 59, 58, 16, 58, 15], "vertices": [1, 48, 41.97, -41.8, 1, 3, 46, 73.47, 27.55, 0.18925, 48, -5.75, -51.71, 0.72419, 47, 112.98, -11.43, 0.08656, 3, 46, 38.23, 10.99, 0.84284, 48, -41.02, -35.22, 0.09706, 47, 92.72, -44.68, 0.06011, 1, 46, 73.36, 10.89, 1, 1, 46, 58.59, -10.38, 1, 2, 45, 75.49, -4.56, 0.10258, 46, 14.36, -24.8, 0.89742, 2, 45, 59.82, -13.73, 0.41734, 46, -2.7, -18.57, 0.58266, 1, 44, 163.07, -108.68, 1, 1, 44, 151.52, -95.05, 1, 1, 44, 110.61, -87.69, 1, 1, 44, 81.05, -86.58, 1, 1, 44, 89.82, -114.32, 1, 1, 44, 68.72, -120.91, 1, 1, 44, 58.1, -115.89, 1, 1, 44, 51.03, -100.63, 1, 1, 44, 38.79, -106.76, 1, 1, 44, 2.68, -89.7, 1, 1, 44, -22.07, -19.3, 1, 1, 44, 1.2, 45.63, 1, 1, 44, 8.07, 64.82, 1, 1, 44, 35.44, 93.73, 1, 1, 44, 59.98, 119.66, 1, 1, 44, 109.26, 136.99, 1, 1, 44, 174.07, 135.27, 1, 2, 44, 205.59, 101.22, 0.83763, 47, -16.8, 104.64, 0.16237, 2, 48, 58.94, 30.5, 0.60736, 47, 38.37, 61.9, 0.39264, 1, 48, 75.56, 19.01, 1, 1, 48, 106.7, 26.9, 1, 1, 48, 83.79, -9.51, 1, 4, 45, 44.52, 27.24, 0.19601, 46, 19.12, 19.33, 0.58067, 48, -46.83, -15.19, 0.07455, 47, 72.17, -48.25, 0.14877, 2, 45, 7.42, 19.08, 0.79203, 47, 34.31, -45.25, 0.20797, 1, 47, 45.94, -9.06, 1, 1, 48, 20.62, -16.35, 1, 1, 48, 75.74, 0.94, 1, 3, 44, 200.44, 40.47, 0.4822, 48, 44.59, 56.29, 0.1495, 47, 11.17, 50.47, 0.3683, 1, 44, 171.41, 90.12, 1, 2, 45, 1.07, 18.93, 0.79203, 47, 28.19, -43.54, 0.20797, 3, 44, 168.13, -6.01, 0.11484, 45, -28.64, 49.04, 0.13133, 47, 8.54, -6.09, 0.75382, 2, 44, 167.83, 37.87, 0.27101, 47, -15.06, 30.91, 0.72899, 1, 44, 162.36, 71.5, 1, 1, 44, 163.11, -47.44, 1, 1, 44, 165.94, -5.87, 1, 1, 44, 165.14, 37.38, 1, 1, 44, 157.6, 71.4, 1, 1, 44, 163.5, -99.54, 1, 1, 44, 45.38, 27.24, 1, 1, 44, 63.74, 44.98, 1, 1, 44, 70.7, 61.93, 1, 1, 44, 62.88, 78.71, 1, 1, 44, 46.53, 85.3, 1, 1, 44, 29.92, 79.34, 1, 1, 44, 15.08, 62.21, 1, 1, 44, 14.09, 45.33, 1, 1, 44, 24.3, 27.06, 1, 1, 44, 48.64, 15.3, 1, 1, 44, 84.87, 62.14, 1, 1, 44, 61.9, 94.84, 1, 1, 44, 22.54, 21.88, 1, 1, 44, 43.15, -95.95, 1, 1, 44, 41.77, -87.24, 1, 1, 44, 60.05, -70.36, 1], "hull": 29, "edges": [10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 56, 54, 56, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 42, 44, 32, 34, 4, 58, 58, 60, 62, 64, 64, 66, 66, 54, 50, 68, 68, 70, 70, 44, 60, 72, 62, 74, 72, 74, 74, 76, 76, 78, 78, 44, 16, 80, 80, 82, 82, 84, 84, 86, 86, 44, 14, 88, 88, 72, 14, 16, 10, 12, 12, 14, 12, 60, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 90, 108, 110, 110, 112, 38, 40, 40, 42, 112, 40, 34, 36, 36, 38, 36, 114, 114, 108, 30, 32, 30, 28, 24, 26, 28, 26, 22, 24, 22, 20, 20, 18, 18, 16, 28, 116, 116, 118, 118, 120, 120, 20], "width": 271, "height": 298}}, "head-bb": {"head": {"type": "boundingbox", "vertexCount": 6, "vertices": [-19.14, -70.3, 40.8, -118.08, 257.78, -115.62, 285.17, 57.18, 120.77, 164.95, -5.07, 76.95]}}, "hoverboard-board": {"hoverboard-board": {"type": "mesh", "uvs": [0.13865, 0.56624, 0.11428, 0.51461, 0.07619, 0.52107, 0.02364, 0.52998, 0.01281, 0.53182, 0, 0.37979, 0, 0.2206, 0.00519, 0.10825, 0.01038, 0.10726, 0.03834, 0.10194, 0.05091, 0, 0.08326, 0, 0.10933, 0.04206, 0.1382, 0.08865, 0.18916, 0.24067, 0.22234, 0.4063, 0.23886, 0.44063, 0.83412, 0.44034, 0.88444, 0.38296, 0.92591, 0.32639, 0.95996, 0.28841, 0.98612, 0.28542, 1, 0.38675, 0.99494, 0.47104, 0.97883, 0.53251, 0.94409, 0.62135, 0.90206, 0.69492, 0.86569, 0.71094, 0.82822, 0.70791, 0.81286, 0.77127, 0.62931, 0.77266, 0.61364, 0.70645, 0.47166, 0.70664, 0.45901, 0.77827, 0.27747, 0.76986, 0.2658, 0.70372, 0.24976, 0.71381, 0.24601, 0.77827, 0.23042, 0.84931, 0.20926, 0.90956, 0.17299, 1, 0.15077, 0.99967, 0.12906, 0.90192, 0.10369, 0.73693, 0.10198, 0.62482, 0.09131, 0.47272, 0.09133, 0.41325, 0.15082, 0.41868, 0.21991, 0.51856, 0.06331, 0.10816, 0.08383, 0.21696, 0.08905, 0.37532, 0.15903, 0.58726, 0.17538, 0.65706, 0.20118, 0.8029, 0.17918, 0.55644, 0.22166, 0.5802, 0.86259, 0.57962, 0.92346, 0.48534, 0.96691, 0.36881, 0.0945, 0.13259, 0.12688, 0.17831, 0.15986, 0.24682, 0.18036, 0.31268, 0.20607, 0.4235, 0.16074, 0.85403, 0.13624, 0.70122, 0.12096, 0.64049, 0.02396, 0.21811, 0.02732, 0.37839, 0.02557, 0.4972, 0.14476, 0.45736, 0.18019, 0.51689, 0.19692, 0.56636], "triangles": [10, 11, 12, 9, 10, 12, 49, 9, 12, 60, 49, 12, 13, 60, 12, 61, 60, 13, 50, 49, 60, 50, 60, 61, 68, 8, 9, 68, 9, 49, 68, 49, 50, 7, 8, 68, 6, 7, 68, 61, 13, 14, 62, 61, 14, 50, 61, 62, 63, 62, 14, 59, 20, 21, 19, 20, 59, 51, 50, 62, 51, 62, 63, 51, 69, 68, 51, 68, 50, 6, 68, 69, 5, 6, 69, 18, 19, 59, 15, 63, 14, 59, 21, 22, 47, 51, 63, 47, 46, 51, 47, 63, 64, 15, 64, 63, 64, 15, 16, 71, 46, 47, 23, 59, 22, 69, 51, 70, 45, 46, 71, 70, 51, 2, 58, 18, 59, 58, 59, 23, 17, 18, 58, 70, 5, 69, 2, 51, 46, 1, 45, 71, 47, 48, 71, 47, 64, 48, 48, 72, 71, 1, 71, 72, 16, 48, 64, 45, 2, 46, 2, 45, 1, 70, 4, 5, 3, 70, 2, 3, 4, 70, 24, 58, 23, 72, 0, 1, 73, 55, 72, 55, 0, 72, 48, 73, 72, 57, 17, 58, 25, 57, 58, 56, 48, 16, 73, 48, 56, 56, 16, 17, 56, 17, 57, 52, 0, 55, 24, 25, 58, 44, 0, 52, 67, 44, 52, 52, 56, 53, 73, 52, 55, 56, 52, 73, 67, 52, 53, 26, 57, 25, 66, 67, 53, 56, 32, 35, 53, 56, 35, 56, 57, 32, 28, 31, 57, 57, 31, 32, 57, 27, 28, 26, 27, 57, 36, 53, 35, 43, 44, 67, 43, 67, 66, 34, 35, 32, 29, 31, 28, 30, 31, 29, 53, 54, 66, 53, 36, 54, 33, 34, 32, 37, 54, 36, 65, 43, 66, 38, 54, 37, 54, 65, 66, 39, 65, 54, 42, 43, 65, 38, 39, 54, 40, 42, 65, 40, 41, 42, 65, 39, 40], "vertices": [-189.36, 15.62, -201.35, 23.47, -220.09, 22.49, -245.95, 21.13, -251.28, 20.86, -257.58, 43.96, -257.57, 68.16, -255.02, 85.24, -252.47, 85.39, -238.71, 86.2, -232.52, 101.69, -216.61, 101.69, -203.78, 95.3, -189.58, 88.21, -164.51, 65.1, -148.19, 39.93, -140.06, 34.71, 152.82, 34.73, 177.57, 43.45, 197.97, 52.05, 214.72, 57.82, 227.6, 58.27, 234.42, 42.87, 231.94, 30.06, 224.01, 20.72, 206.91, 7.21, 186.23, -3.97, 168.34, -6.4, 149.9, -5.94, 142.35, -15.57, 52.04, -15.77, 44.33, -5.71, -25.52, -5.73, -31.75, -16.62, -121.07, -15.34, -126.81, -5.28, -134.7, -6.81, -136.54, -16.61, -144.22, -27.41, -154.63, -36.57, -172.47, -50.31, -183.41, -50.26, -194.09, -35.4, -206.56, -10.32, -207.4, 6.72, -212.65, 29.84, -212.64, 38.88, -183.37, 38.05, -149.38, 22.86, -226.43, 85.25, -216.33, 68.71, -213.76, 44.64, -179.34, 12.42, -171.29, 1.81, -158.6, -20.36, -169.42, 17.11, -148.52, 13.49, 166.82, 13.56, 196.76, 27.89, 218.14, 45.6, -211.08, 81.54, -195.15, 74.59, -178.93, 64.17, -168.84, 54.16, -156.19, 37.31, -178.5, -28.13, -190.55, -4.9, -198.07, 4.33, -245.79, 68.54, -244.14, 44.18, -245, 26.12, -186.36, 32.17, -168.92, 23.12, -160.69, 15.6], "hull": 45, "edges": [0, 2, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 80, 82, 82, 84, 84, 86, 86, 88, 0, 88, 2, 90, 90, 92, 92, 94, 94, 96, 96, 32, 18, 98, 98, 100, 100, 102, 2, 4, 102, 4, 92, 102, 0, 104, 104, 106, 106, 108, 78, 80, 108, 78, 74, 76, 76, 78, 62, 56, 64, 70, 0, 110, 112, 114, 114, 116, 116, 118, 118, 42, 50, 116, 114, 34, 98, 120, 120, 122, 22, 24, 24, 26, 120, 24, 122, 124, 124, 126, 126, 128, 128, 96, 80, 130, 130, 132, 132, 134, 134, 88, 14, 16, 16, 18, 136, 16, 136, 138, 138, 140, 4, 6, 6, 8, 140, 6, 96, 112, 92, 142, 142, 144, 110, 146, 146, 112, 144, 146], "width": 492, "height": 152}}, "hoverboard-thruster-front": {"hoverboard-thruster": {"x": 0.02, "y": -7.08, "rotation": 0.17, "width": 60, "height": 64}}, "hoverboard-thruster-rear": {"hoverboard-thruster": {"x": 1.1, "y": -6.29, "rotation": 0.17, "width": 60, "height": 64}}, "hoverglow-front": {"hoverglow-small": {"x": 2.13, "y": -2, "scaleX": 0.303, "scaleY": 0.495, "rotation": 0.15, "width": 274, "height": 75}}, "hoverglow-rear": {"hoverglow-small": {"x": 1.39, "y": -2.09, "scaleX": 0.303, "scaleY": 0.495, "rotation": 0.61, "width": 274, "height": 75}}, "mouth": {"mouth-grind": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [11.28, -85.88, -19.56, 1.84, 36.09, 21.41, 66.93, -66.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 59}, "mouth-oooo": {"x": 23.69, "y": -32.24, "rotation": -70.63, "width": 93, "height": 59}, "mouth-smile": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.28, -85.89, -19.56, 1.85, 36.1, 21.42, 66.94, -66.32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 59}}, "muzzle": {"muzzle01": {"x": 151.97, "y": 5.81, "scaleX": 3.7361, "scaleY": 3.7361, "rotation": 0.15, "width": 133, "height": 79}, "muzzle02": {"x": 187.25, "y": 5.9, "scaleX": 4.0623, "scaleY": 4.0623, "rotation": 0.15, "width": 135, "height": 84}, "muzzle03": {"x": 231.96, "y": 6.02, "scaleX": 4.1325, "scaleY": 4.1325, "rotation": 0.15, "width": 166, "height": 106}, "muzzle04": {"x": 231.96, "y": 6.02, "scaleX": 4.0046, "scaleY": 4.0046, "rotation": 0.15, "width": 149, "height": 90}, "muzzle05": {"x": 293.8, "y": 6.19, "scaleX": 4.4673, "scaleY": 4.4673, "rotation": 0.15, "width": 135, "height": 75}}, "muzzle-glow": {"muzzle-glow": {"width": 50, "height": 50}}, "muzzle-ring": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "muzzle-ring2": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "muzzle-ring3": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "muzzle-ring4": {"muzzle-ring": {"x": -1.3, "y": 0.32, "scaleX": 0.3147, "scaleY": 0.3147, "width": 49, "height": 209}}, "neck": {"neck": {"x": 9.77, "y": -3.01, "rotation": -55.22, "width": 36, "height": 41}}, "portal-bg": {"portal-bg": {"x": -3.1, "y": 7.25, "scaleX": 1.0492, "scaleY": 1.0492, "width": 266, "height": 266}}, "portal-flare1": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare10": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare2": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare3": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare4": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare5": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare6": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare7": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare8": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-flare9": {"portal-flare1": {"width": 111, "height": 60}, "portal-flare2": {"width": 114, "height": 61}, "portal-flare3": {"width": 115, "height": 59}}, "portal-shade": {"portal-shade": {"width": 266, "height": 266}}, "portal-streaks1": {"portal-streaks1": {"scaleX": 0.9774, "scaleY": 0.9774, "width": 252, "height": 256}}, "portal-streaks2": {"portal-streaks2": {"x": -1.64, "y": 2.79, "width": 250, "height": 249}}, "rear-bracer": {"rear-bracer": {"x": 11.15, "y": -2.2, "rotation": 66.17, "width": 56, "height": 72}}, "rear-foot": {"rear-foot": {"type": "mesh", "uvs": [0.48368, 0.1387, 0.51991, 0.21424, 0.551, 0.27907, 0.58838, 0.29816, 0.63489, 0.32191, 0.77342, 0.39267, 1, 0.73347, 1, 1, 0.54831, 0.99883, 0.31161, 1, 0, 1, 0, 0.41397, 0.13631, 0, 0.41717, 0], "triangles": [8, 3, 4, 8, 4, 5, 8, 5, 6, 8, 6, 7, 11, 1, 10, 3, 9, 2, 2, 10, 1, 12, 13, 0, 0, 11, 12, 1, 11, 0, 2, 9, 10, 3, 8, 9], "vertices": [2, 8, 10.45, 29.41, 0.90802, 9, -6.74, 49.62, 0.09198, 2, 8, 16.56, 29.27, 0.84259, 9, -2.65, 45.09, 0.15741, 2, 8, 21.8, 29.15, 0.69807, 9, 0.85, 41.2, 0.30193, 2, 8, 25.53, 31.43, 0.52955, 9, 5.08, 40.05, 0.47045, 2, 8, 30.18, 34.27, 0.39303, 9, 10.33, 38.62, 0.60697, 2, 8, 44.02, 42.73, 0.27525, 9, 25.98, 34.36, 0.72475, 2, 8, 76.47, 47.28, 0.21597, 9, 51.56, 13.9, 0.78403, 2, 8, 88.09, 36.29, 0.28719, 9, 51.55, -2.09, 0.71281, 2, 8, 52.94, -0.73, 0.47576, 9, 0.52, -1.98, 0.52424, 2, 8, 34.63, -20.23, 0.68757, 9, -26.23, -2.03, 0.31243, 2, 8, 10.44, -45.81, 0.84141, 9, -61.43, -2, 0.15859, 2, 8, -15.11, -21.64, 0.93283, 9, -61.4, 33.15, 0.06717, 1, 8, -22.57, 6.61, 1, 1, 8, -0.76, 29.67, 1], "hull": 14, "edges": [14, 12, 10, 12, 14, 16, 16, 18, 18, 20, 4, 18, 20, 22, 24, 26, 22, 24, 4, 2, 2, 20, 4, 6, 6, 16, 6, 8, 8, 10, 2, 0, 0, 26], "width": 113, "height": 60}}, "rear-shin": {"rear-shin": {"x": 58.29, "y": -2.75, "rotation": 92.37, "width": 75, "height": 178}}, "rear-thigh": {"rear-thigh": {"x": 33.11, "y": -4.11, "rotation": 72.54, "width": 55, "height": 94}}, "rear-upper-arm": {"rear-upper-arm": {"x": 21.13, "y": 4.09, "rotation": 89.33, "width": 40, "height": 87}}, "side-glow1": {"hoverglow-small": {"x": 2.09, "scaleX": 0.2353, "scaleY": 0.4132, "width": 274, "height": 75}}, "side-glow2": {"hoverglow-small": {"x": 2.09, "scaleX": 0.2353, "scaleY": 0.4132, "width": 274, "height": 75}}, "side-glow3": {"hoverglow-small": {"x": 2.09, "scaleX": 0.3586, "scaleY": 0.6297, "width": 274, "height": 75}}, "torso": {"torso": {"type": "mesh", "uvs": [0.6251, 0.12672, 1, 0.26361, 1, 0.28871, 1, 0.66021, 1, 0.68245, 0.92324, 0.69259, 0.95116, 0.84965, 0.77124, 1, 0.49655, 1, 0.27181, 1, 0.13842, 0.77196, 0.09886, 0.6817, 0.05635, 0.58471, 0, 0.45614, 0, 0.33778, 0, 0.19436, 0.14463, 0, 0.27802, 0, 0.72525, 0.27835, 0.76091, 0.46216, 0.84888, 0.67963, 0.68257, 0.63249, 0.53986, 0.3847, 0.25443, 0.3217, 0.30063, 0.55174, 0.39553, 0.79507, 0.26389, 0.17007, 0.5241, 0.18674, 0.71492, 0.76655, 0.82151, 0.72956, 0.27626, 0.4304, 0.62327, 0.52952, 0.3455, 0.66679, 0.53243, 0.2914], "triangles": [19, 18, 2, 13, 14, 23, 23, 33, 22, 22, 33, 18, 14, 15, 23, 33, 26, 27, 33, 23, 26, 23, 15, 26, 33, 27, 18, 18, 1, 2, 27, 0, 18, 18, 0, 1, 15, 16, 26, 0, 27, 17, 17, 27, 16, 27, 26, 16, 11, 24, 32, 11, 12, 24, 3, 20, 19, 32, 31, 21, 32, 24, 31, 19, 2, 3, 21, 31, 19, 12, 30, 24, 12, 13, 30, 24, 22, 31, 24, 30, 22, 31, 22, 19, 22, 18, 19, 13, 23, 30, 30, 23, 22, 8, 28, 7, 7, 29, 6, 7, 28, 29, 9, 25, 8, 8, 25, 28, 9, 10, 25, 29, 5, 6, 10, 32, 25, 25, 21, 28, 25, 32, 21, 10, 11, 32, 28, 21, 29, 29, 20, 5, 29, 21, 20, 4, 5, 3, 5, 20, 3, 20, 21, 19], "vertices": [1, 29, 44.59, -10.39, 1, 2, 28, 59.65, -45.08, 0.31254, 29, 17.13, -45.08, 0.68746, 2, 28, 55.15, -44.72, 0.34488, 29, 12.63, -44.72, 0.65512, 2, 27, 31.01, -39.45, 0.62357, 28, -11.51, -39.45, 0.37643, 2, 27, 27.01, -39.14, 0.65234, 28, -15.5, -39.14, 0.34766, 2, 27, 25.79, -31.5, 0.75532, 28, -16.73, -31.5, 0.24468, 1, 27, -2.61, -32, 1, 1, 27, -28.2, -12.29, 1, 1, 27, -26.08, 14.55, 1, 1, 27, -24.35, 36.5, 1, 2, 27, 17.6, 46.3, 0.8332, 28, -24.92, 46.3, 0.1668, 2, 27, 34.1, 48.89, 0.59943, 28, -8.42, 48.89, 0.40058, 3, 27, 51.83, 51.67, 0.29262, 28, 9.32, 51.67, 0.63181, 29, -33.2, 51.67, 0.07557, 3, 27, 75.34, 55.35, 0.06656, 28, 32.82, 55.35, 0.62298, 29, -9.7, 55.35, 0.31046, 2, 28, 54.06, 53.67, 0.37296, 29, 11.54, 53.67, 0.62704, 2, 28, 79.79, 51.64, 0.10373, 29, 37.27, 51.64, 0.89627, 1, 29, 71.04, 34.76, 1, 1, 29, 70.01, 21.72, 1, 2, 28, 59.13, -18.02, 0.12067, 29, 16.61, -18.02, 0.87933, 2, 28, 25.87, -18.9, 0.91272, 29, -16.65, -18.9, 0.08728, 2, 27, 28.69, -24.42, 0.77602, 28, -13.83, -24.42, 0.22398, 2, 27, 38.43, -8.84, 0.7254, 28, -4.09, -8.84, 0.2746, 2, 28, 41.48, 1.59, 0.75167, 29, -1.04, 1.59, 0.24833, 2, 28, 54.98, 28.59, 0.27889, 29, 12.46, 28.59, 0.72111, 2, 27, 55.87, 27.33, 0.21124, 28, 13.35, 27.33, 0.78876, 1, 27, 11.47, 21.51, 1, 1, 29, 39.6, 25.51, 1, 1, 29, 34.6, 0.33, 1, 1, 27, 14.12, -10.1, 1, 2, 27, 19.94, -21.03, 0.92029, 28, -22.58, -21.03, 0.07971, 2, 28, 35.31, 27.99, 0.69833, 29, -7.21, 27.99, 0.30167, 1, 28, 14.84, -4.5, 1, 2, 27, 34.87, 24.58, 0.67349, 28, -7.64, 24.58, 0.32651, 1, 29, 15.76, 1, 1], "hull": 18, "edges": [14, 12, 12, 10, 10, 8, 18, 20, 32, 34, 30, 32, 2, 4, 36, 4, 36, 38, 38, 40, 4, 6, 6, 8, 40, 6, 40, 42, 14, 16, 16, 18, 50, 16, 46, 52, 54, 36, 2, 0, 0, 34, 54, 0, 54, 32, 20, 50, 14, 56, 56, 42, 50, 56, 56, 58, 58, 40, 58, 10, 46, 60, 60, 48, 26, 60, 60, 44, 24, 26, 24, 48, 42, 62, 62, 44, 48, 62, 48, 64, 64, 50, 42, 64, 20, 22, 22, 24, 64, 22, 26, 28, 28, 30, 28, 46, 44, 66, 66, 54, 46, 66, 66, 36, 62, 38], "width": 98, "height": 180}}}}], "events": {"footstep": {}}, "animations": {"aim": {"slots": {"crosshair": {"attachment": [{"name": "crosshair"}]}}, "bones": {"front-fist": {"rotate": [{"angle": 36.08}]}, "rear-bracer": {"rotate": [{"angle": -26.55}]}, "rear-upper-arm": {"rotate": [{"angle": 62.31}]}, "front-bracer": {"rotate": [{"angle": 9.11}]}, "gun": {"rotate": [{"angle": -0.31}]}}, "ik": {"aim-ik": [{"mix": 0.995}]}, "transform": {"aim-front-arm-transform": [{"rotateMix": 0.784, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "aim-head-transform": [{"rotateMix": 0.659, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "aim-torso-transform": [{"rotateMix": 0.423, "translateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"eye": {"eye-indifferent": [{"vertices": [-0.68777, -17.26618, -0.68777, -17.26618, -0.68777, -17.26618, -0.68777, -17.26618]}]}, "goggles": {"goggles": [{"offset": 16, "vertices": [-0.18341, -4.60426, -0.25211, -6.33094]}]}, "head": {"head": [{"offset": 34, "vertices": [-0.22919, -5.75542, -0.22919, -5.75542, -0.22919, -5.75542]}]}, "mouth": {"mouth-smile": [{"vertices": [5.66431, 2.18625, 0.48294, -15.04339, 0.53525, -20.30316, -7.72803, -7.72495]}]}}}}, "death": {"slots": {"eye": {"attachment": [{"name": "eye-surprised"}, {"time": 0.4667, "name": "eye-indifferent"}, {"time": 2.2333, "name": "eye-surprised"}, {"time": 4.5333, "name": "eye-indifferent"}]}, "front-fist": {"attachment": [{"name": "front-fist-open"}]}, "mouth": {"attachment": [{"name": "mouth-oooo"}, {"time": 2.2333, "name": "mouth-grind"}, {"time": 4.5333, "name": "mouth-oooo"}]}}, "bones": {"head": {"rotate": [{"angle": -2.83}, {"time": 0.1333, "angle": -28.74}, {"time": 0.2333, "angle": 11.43}, {"time": 0.3333, "angle": -50.25}, {"time": 0.4, "angle": -72.67, "curve": "stepped"}, {"time": 0.4333, "angle": -72.67}, {"time": 0.5, "angle": -20.25}, {"time": 0.5667, "angle": -85.29, "curve": "stepped"}, {"time": 2.2333, "angle": -85.29}, {"time": 2.5, "angle": -51.96, "curve": "stepped"}, {"time": 4.5333, "angle": -51.96}, {"time": 4.6667, "angle": -85.29}]}, "neck": {"rotate": [{"angle": -2.83}, {"time": 0.1333, "angle": 12.35}, {"time": 0.2333, "angle": 29.89}, {"time": 0.3, "angle": 70.36}, {"time": 0.4, "angle": -10.22, "curve": "stepped"}, {"time": 0.4333, "angle": -10.22}, {"time": 0.5, "angle": 2.93}, {"time": 0.5667, "angle": 47.95, "curve": "stepped"}, {"time": 2.2333, "angle": 47.95}, {"time": 2.5, "angle": 18.51, "curve": "stepped"}, {"time": 4.5333, "angle": 18.51}, {"time": 4.6667, "angle": 47.95}]}, "torso": {"rotate": [{"angle": -8.62}, {"time": 0.1333, "angle": 28.2}, {"time": 0.2667, "angle": -280.19}, {"time": 0.4, "angle": -237.23, "curve": "stepped"}, {"time": 0.4333, "angle": -237.23}, {"time": 0.5, "angle": 76.03}]}, "front-upper-arm": {"rotate": [{"angle": -38.86}, {"time": 0.1333, "angle": -299.59}, {"time": 0.2667, "angle": -244.75}, {"time": 0.4, "angle": -292.36}, {"time": 0.4333, "angle": -315.85}, {"time": 0.5, "angle": -347.94}, {"time": 0.7, "angle": -347.33, "curve": "stepped"}, {"time": 2.2333, "angle": -347.33}, {"time": 2.7, "angle": -290.68}, {"time": 2.7667, "angle": -285.11}, {"time": 4.6667, "angle": -290.68}, {"time": 4.8, "angle": 8.61}, {"time": 4.8667, "angle": 10.94}]}, "rear-upper-arm": {"rotate": [{"angle": -44.7}, {"time": 0.1333, "angle": 112.26}, {"time": 0.2667, "angle": 129.08}, {"time": 0.4, "angle": 134.94, "curve": "stepped"}, {"time": 0.4333, "angle": 134.94}, {"time": 0.5667, "angle": 172.6}]}, "front-bracer": {"rotate": [{"angle": 21.88}, {"time": 0.1333, "angle": 11.49}, {"time": 0.2667, "angle": -18.82}, {"time": 0.4, "angle": -18.93}, {"time": 0.4333, "angle": -18.28}, {"time": 0.5, "angle": 60.62}, {"time": 0.7, "angle": -18.88, "curve": "stepped"}, {"time": 2.2333, "angle": -18.88}, {"time": 2.7, "angle": -1.96, "curve": "stepped"}, {"time": 4.6667, "angle": -1.96}, {"time": 4.8, "angle": 34.55}, {"time": 4.9333, "angle": -18.75}]}, "front-fist": {"rotate": [{"angle": -2.33}, {"time": 0.2667, "angle": 26.35}, {"time": 0.7, "angle": -6.08, "curve": "stepped"}, {"time": 2.2333, "angle": -6.08}, {"time": 2.7, "angle": 5.73, "curve": "stepped"}, {"time": 4.6667, "angle": 5.73}, {"time": 4.8667, "angle": -6.52}]}, "rear-bracer": {"rotate": [{"angle": 10.36}, {"time": 0.1333, "angle": -23.12}, {"time": 0.2667, "angle": -23.12}, {"time": 0.4, "angle": -23.16, "curve": "stepped"}, {"time": 0.4333, "angle": -23.16}, {"time": 0.5667, "angle": -23.2}]}, "gun": {"rotate": [{"angle": -2.79}, {"time": 0.1333, "angle": -24.58}]}, "hip": {"translate": [{}, {"time": 0.2, "x": 50.35, "y": 151.73}, {"time": 0.4, "x": 5.17, "y": -119.65, "curve": "stepped"}, {"time": 0.4333, "x": 5.17, "y": -119.65}, {"time": 0.5, "x": 50.35, "y": -205.19}]}, "front-thigh": {"rotate": [{}, {"time": 0.1333, "angle": 8.47}, {"time": 0.2667, "angle": 115.96}, {"time": 0.4, "angle": 180.66, "curve": "stepped"}, {"time": 0.4333, "angle": 180.66}, {"time": 0.5, "angle": 155.22}, {"time": 0.6, "angle": 97.74}]}, "front-shin": {"rotate": [{}, {"time": 0.1333, "angle": -27.37}, {"time": 0.2667, "angle": -35.1}, {"time": 0.4, "angle": -37.73, "curve": "stepped"}, {"time": 0.4333, "angle": -37.73}, {"time": 0.5, "angle": -40.07}, {"time": 0.6, "angle": 2.76}]}, "rear-thigh": {"rotate": [{}, {"time": 0.1333, "angle": 70.45}, {"time": 0.2667, "angle": 155.35}, {"time": 0.4, "angle": 214.31, "curve": "stepped"}, {"time": 0.4333, "angle": 214.31}, {"time": 0.5, "angle": 169.67}, {"time": 0.8, "angle": 83.27}]}, "rear-shin": {"rotate": [{}, {"time": 0.1333, "angle": 18.94}, {"time": 0.2667, "angle": -21.04}, {"time": 0.4, "angle": -29.94, "curve": "stepped"}, {"time": 0.4333, "angle": -29.94}, {"time": 0.5, "angle": -16.79}, {"time": 0.8, "angle": 7.78}]}, "rear-foot": {"rotate": [{}, {"time": 0.1333, "angle": -11.63}, {"time": 0.4, "angle": -45.6}]}, "front-foot": {"rotate": [{}, {"time": 0.4, "angle": -48.75}]}, "front-foot-tip": {"rotate": [{}, {"time": 0.1333, "angle": -43.25}, {"time": 0.2, "angle": 6.05}, {"time": 0.3, "angle": 36.84}, {"time": 0.3667, "angle": 74.42}, {"time": 0.5667, "angle": 77.34}, {"time": 0.7, "angle": 59.35}]}, "back-foot-tip": {"rotate": [{}, {"time": 0.1333, "angle": 83.04}, {"time": 0.3, "angle": 100.03}, {"time": 0.3667, "angle": 118.36}, {"time": 0.5667, "angle": 115.44}, {"time": 0.7, "angle": 88.21}, {"time": 0.8333, "angle": 53.38}]}, "hair4": {"rotate": [{}, {"time": 0.2, "angle": -23.42}, {"time": 0.3, "angle": -16.06}, {"time": 0.3333, "angle": 19.03}, {"time": 0.4333, "angle": -4.91}, {"time": 0.5667, "angle": 1.29}]}, "hair2": {"rotate": [{}, {"time": 0.2, "angle": -23.42}, {"time": 0.3, "angle": -16.06}, {"time": 0.3333, "angle": 19.03}, {"time": 0.4333, "angle": -4.91}, {"time": 0.5667, "angle": 1.29}]}}, "ik": {"front-foot-ik": [{"mix": 0}], "front-leg-ik": [{"mix": 0, "bendPositive": false}], "rear-foot-ik": [{"mix": 0.005}], "rear-leg-ik": [{"mix": 0.005, "bendPositive": false}]}}, "hoverboard": {"slots": {"exhaust1": {"attachment": [{"name": "hoverglow-small"}]}, "exhaust2": {"attachment": [{"name": "hoverglow-small"}]}, "exhaust3": {"attachment": [{"name": "hoverglow-small"}]}, "front-fist": {"attachment": [{"name": "front-fist-open"}]}, "hoverboard-board": {"attachment": [{"name": "hoverboard-board"}]}, "hoverboard-thruster-front": {"attachment": [{"name": "hoverboard-thruster"}]}, "hoverboard-thruster-rear": {"attachment": [{"name": "hoverboard-thruster"}]}, "hoverglow-front": {"attachment": [{"name": "hoverglow-small"}]}, "hoverglow-rear": {"attachment": [{"name": "hoverglow-small"}]}, "side-glow1": {"attachment": [{"name": "hoverglow-small"}, {"time": 0.9667, "name": null}]}, "side-glow2": {"attachment": [{"time": 0.0667, "name": "hoverglow-small"}, {"time": 1, "name": null}]}, "side-glow3": {"attachment": [{"name": "hoverglow-small"}, {"time": 0.9667, "name": null}]}}, "bones": {"hoverboard-controller": {"translate": [{"x": 319.55, "y": -1.59, "curve": 0.545, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": 347.66, "y": 47.75, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5333, "x": 338.47, "y": 85.72, "curve": 0.25, "c3": 0.522, "c4": 0.99}, {"time": 1, "x": 319.55, "y": -1.59}]}, "hip": {"translate": [{"x": -53.49, "y": 32.14, "curve": 0.279, "c2": 0.27, "c3": 0.677, "c4": 0.99}, {"time": 0.1333, "x": -49.31, "y": 23.31, "curve": 0.417, "c3": 0.75}, {"time": 0.3333, "x": -33.64, "y": 50.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -20.06, "y": 122.72, "curve": 0.429, "c2": 0.01, "c3": 0.685, "c4": 0.35}, {"time": 1, "x": -53.49, "y": 32.14}]}, "exhaust1": {"scale": [{"x": 1.593, "y": 0.964}, {"time": 0.1333, "y": 0.713}, {"time": 0.2, "x": 1.774, "y": 0.883}, {"time": 0.3667, "x": 1.181, "y": 0.649}, {"time": 0.5333, "x": 1.893, "y": 0.819}, {"time": 0.6333, "x": 1.18, "y": 0.686}, {"time": 0.7333, "x": 1.903, "y": 0.855}, {"time": 0.8667, "x": 1.311, "y": 0.622}, {"time": 1, "x": 1.593, "y": 0.964}]}, "exhaust2": {"scale": [{"x": 1.88, "y": 0.832}, {"time": 0.1, "x": 1.311, "y": 0.686}, {"time": 0.2333, "x": 2.01, "y": 0.769}, {"time": 0.3667, "y": 0.794}, {"time": 0.5, "x": 1.699, "y": 0.86}, {"time": 0.5667, "x": 1.181, "y": 0.713}, {"time": 0.7667, "x": 1.881, "y": 0.796}, {"time": 0.9, "x": 1.3, "y": 0.649}, {"time": 1, "x": 1.88, "y": 0.832}]}, "hoverboard-thruster-front": {"rotate": [{}, {"time": 0.5, "angle": 24.06}, {"time": 1}]}, "hoverglow-front": {"scale": [{"x": 0.849, "y": 1.764}, {"time": 0.0667, "x": 0.835, "y": 2.033}, {"time": 0.1667, "x": 0.752, "y": 1.735}, {"time": 0.2333, "x": 0.809, "y": 1.71}, {"time": 0.3, "x": 0.717, "y": 1.45}, {"time": 0.3667, "x": 0.777, "y": 1.45}, {"time": 0.4, "x": 0.725, "y": 1.241}, {"time": 0.4667, "x": 0.685, "y": 1.173}, {"time": 0.5667, "x": 0.825, "y": 1.572}, {"time": 0.6, "x": 0.758, "y": 1.297}, {"time": 0.6667, "x": 0.725, "y": 1.241}, {"time": 0.7667, "x": 0.895, "y": 1.857}, {"time": 0.8333, "x": 0.845, "y": 1.962}, {"time": 0.9, "x": 0.802, "y": 1.491}, {"time": 0.9667, "x": 0.845, "y": 1.31}, {"time": 1, "x": 0.849, "y": 1.764}]}, "hoverboard-thruster-rear": {"rotate": [{}, {"time": 0.5, "angle": 24.06}, {"time": 1}]}, "hoverglow-rear": {"scale": [{"x": 0.845, "y": 1.31}, {"time": 0.0667, "x": 0.856, "y": 1.629}, {"time": 0.1333, "x": 0.835, "y": 2.033}, {"time": 0.2, "x": 0.752, "y": 1.735}, {"time": 0.3, "x": 0.809, "y": 1.71}, {"time": 0.3667, "x": 0.717, "y": 1.45}, {"time": 0.4333, "x": 0.777, "y": 1.45}, {"time": 0.5, "x": 0.725, "y": 1.241}, {"time": 0.5667, "x": 0.685, "y": 1.173}, {"time": 0.6333, "x": 0.758, "y": 1.297}, {"time": 0.7333, "x": 0.725, "y": 1.241}, {"time": 0.7667, "x": 0.825, "y": 1.572}, {"time": 0.8333, "x": 0.895, "y": 1.857}, {"time": 0.9, "x": 0.845, "y": 1.962}, {"time": 0.9667, "x": 0.802, "y": 1.491}, {"time": 1, "x": 0.845, "y": 1.31}]}, "front-upper-arm": {"rotate": [{"angle": -85.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -53.64, "curve": 0.722, "c3": 0.75}, {"time": 0.6333, "angle": -79.62, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -85.92}], "translate": [{"x": -0.59, "y": -2.94}, {"time": 0.2667, "x": -6.76, "y": -11.66}, {"time": 0.3667, "x": -1.74, "y": -6.39}, {"time": 0.6333, "x": 0.72, "y": -2.88}, {"time": 1, "x": -0.59, "y": -2.94}]}, "front-fist": {"rotate": [{"angle": 16.07}, {"time": 0.2667, "angle": -26.01}, {"time": 0.5667, "angle": 21.48}, {"time": 1, "angle": 16.07}], "translate": [{}, {"time": 0.4667, "x": 0.52, "y": -3.27}, {"time": 1}], "shear": [{"y": 19.83}, {"time": 0.4667, "x": 15.28, "y": 28.31}, {"time": 1, "y": 19.83}]}, "board-ik": {"translate": [{"x": 393.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 393.48, "y": 117.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 393.62, "y": 83.82}, {"time": 0.6667, "x": 393.62, "y": 30.15}, {"time": 1, "x": 393.62}]}, "front-thigh": {"translate": [{"x": -7.49, "y": 8.51}]}, "front-leg-target": {"translate": [{"time": 0.3667}, {"time": 0.5, "x": 12.78, "y": 8.79}, {"time": 0.8667}]}, "rear-leg-target": {"translate": [{"time": 0.4667}, {"time": 0.5667, "x": 4.53, "y": 1.77}, {"time": 0.6667, "x": -1.05, "y": -0.44}, {"time": 1}]}, "exhaust3": {"scale": [{"x": 1.882, "y": 0.81}, {"time": 0.0667, "x": 1.731, "y": 0.761}, {"time": 0.2, "x": 1.3, "y": 0.649}, {"time": 0.3, "x": 2.051, "y": 0.984}, {"time": 0.4, "x": 1.311, "y": 0.686}, {"time": 0.5333, "x": 1.86, "y": 0.734}, {"time": 0.6667, "y": 0.794}, {"time": 0.8, "x": 1.549, "y": 0.825}, {"time": 0.8667, "x": 1.181, "y": 0.713}, {"time": 1, "x": 1.731, "y": 0.78}]}, "side-glow1": {"rotate": [{"angle": 51.12, "curve": "stepped"}, {"time": 0.0667, "angle": 43.82, "curve": "stepped"}, {"time": 0.1, "angle": 40.95, "curve": "stepped"}, {"time": 0.1667, "angle": 27.78, "curve": "stepped"}, {"time": 0.2, "angle": 10.24, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.8, "angle": -25.81}], "translate": [{"x": 338.28, "y": 40.22, "curve": "stepped"}, {"time": 0.0667, "x": 331.2, "y": 30.39, "curve": "stepped"}, {"time": 0.1, "x": 318.63, "y": 20.59, "curve": "stepped"}, {"time": 0.1667, "x": 302.45, "y": 9.64, "curve": "stepped"}, {"time": 0.2, "x": 276.87, "y": 1.13, "curve": "stepped"}, {"time": 0.2667, "x": 248.16, "curve": "stepped"}, {"time": 0.3, "x": 221.36, "curve": "stepped"}, {"time": 0.3667, "x": 195.69, "curve": "stepped"}, {"time": 0.4, "x": 171.08, "curve": "stepped"}, {"time": 0.4667, "x": 144.84, "curve": "stepped"}, {"time": 0.5, "x": 121.22, "curve": "stepped"}, {"time": 0.5667, "x": 91.98, "curve": "stepped"}, {"time": 0.6, "x": 62.63, "curve": "stepped"}, {"time": 0.6667, "x": 30.78, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.7667, "x": -28.45, "curve": "stepped"}, {"time": 0.8, "x": -67.49, "y": 16.82, "curve": "stepped"}, {"time": 0.8667, "x": -83.07, "y": 24.36, "curve": "stepped"}, {"time": 0.9, "x": -93.81, "y": 29.55}], "scale": [{"x": 0.535, "curve": "stepped"}, {"time": 0.0667, "x": 0.594, "curve": "stepped"}, {"time": 0.1, "x": 0.844, "curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.8, "x": 0.534, "curve": "stepped"}, {"time": 0.8667, "x": 0.428, "y": 0.801, "curve": "stepped"}, {"time": 0.9, "x": 0.349, "y": 0.654}]}, "side-glow2": {"rotate": [{"time": 0.0667, "angle": 51.12, "curve": "stepped"}, {"time": 0.1, "angle": 43.82, "curve": "stepped"}, {"time": 0.1667, "angle": 40.95, "curve": "stepped"}, {"time": 0.2, "angle": 27.78, "curve": "stepped"}, {"time": 0.2667, "angle": 10.24, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.8667, "angle": -25.81}], "translate": [{"time": 0.0667, "x": 338.28, "y": 40.22, "curve": "stepped"}, {"time": 0.1, "x": 331.2, "y": 30.39, "curve": "stepped"}, {"time": 0.1667, "x": 318.63, "y": 20.59, "curve": "stepped"}, {"time": 0.2, "x": 302.45, "y": 9.64, "curve": "stepped"}, {"time": 0.2667, "x": 276.87, "y": 1.13, "curve": "stepped"}, {"time": 0.3, "x": 248.16, "curve": "stepped"}, {"time": 0.3667, "x": 221.36, "curve": "stepped"}, {"time": 0.4, "x": 195.69, "curve": "stepped"}, {"time": 0.4667, "x": 171.08, "curve": "stepped"}, {"time": 0.5, "x": 144.84, "curve": "stepped"}, {"time": 0.5667, "x": 121.22, "curve": "stepped"}, {"time": 0.6, "x": 91.98, "curve": "stepped"}, {"time": 0.6667, "x": 62.63, "curve": "stepped"}, {"time": 0.7, "x": 30.78, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8, "x": -28.45, "curve": "stepped"}, {"time": 0.8667, "x": -67.49, "y": 16.82, "curve": "stepped"}, {"time": 0.9, "x": -83.07, "y": 24.36, "curve": "stepped"}, {"time": 0.9667, "x": -93.81, "y": 29.55}], "scale": [{"time": 0.0667, "x": 0.535, "curve": "stepped"}, {"time": 0.1, "x": 0.594, "curve": "stepped"}, {"time": 0.1667, "x": 0.844, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.8667, "x": 0.534, "curve": "stepped"}, {"time": 0.9, "x": 0.428, "y": 0.801, "curve": "stepped"}, {"time": 0.9667, "x": 0.349, "y": 0.654}]}, "torso": {"rotate": [{"angle": -34.73, "curve": 0.438, "c3": 0.75}, {"time": 0.2667, "angle": -39.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -28.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -21.01}, {"time": 1, "angle": -34.73}]}, "neck": {"rotate": [{"angle": 10.2}, {"time": 0.2667, "angle": 16.14}, {"time": 0.5, "angle": 5.83}, {"time": 0.6333, "angle": 2.68}, {"time": 1, "angle": 10.2}]}, "head": {"rotate": [{"angle": 10.2}, {"time": 0.2667, "angle": 16.14}, {"time": 0.5, "angle": 5.83}, {"time": 0.6333, "angle": 2.68}, {"time": 1, "angle": 10.2}], "translate": [{}, {"time": 0.2667, "x": -4.22, "y": -3.62}, {"time": 0.6333, "x": 0.84, "y": 6.01}, {"time": 1}]}, "front-bracer": {"rotate": [{"angle": -11.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 6.91, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.18}]}, "hair3": {"rotate": [{"angle": 9.61, "curve": "stepped"}, {"time": 0.3667, "angle": 9.61}, {"time": 0.5, "angle": -8.42}, {"time": 1, "angle": 9.61}]}, "hair4": {"rotate": [{"angle": -17.7}, {"time": 0.0333, "angle": -9.09}, {"time": 0.0667, "angle": -9.34}, {"time": 0.1, "angle": -3.31}, {"time": 0.1667, "angle": 0.65}, {"time": 0.2, "angle": 5.23}, {"time": 0.2667, "angle": 17.56}, {"time": 0.3667, "angle": 27.97}, {"time": 0.5, "angle": -1.45}, {"time": 0.5667, "angle": -1.78}, {"time": 0.6333, "angle": -8.9}, {"time": 0.6667, "angle": -5.4}, {"time": 0.7333, "angle": -15.32}, {"time": 0.7667, "angle": -9.19}, {"time": 0.8333, "angle": -23.6}, {"time": 0.8667, "angle": -22.7}, {"time": 0.9333, "angle": -17.38}, {"time": 0.9667, "angle": -18.96}, {"time": 1, "angle": -17.7}]}, "hair1": {"rotate": [{"angle": 9.61, "curve": "stepped"}, {"time": 0.3667, "angle": 9.61}, {"time": 0.5, "angle": -8.42}, {"time": 1, "angle": 9.61}]}, "hair2": {"rotate": [{"angle": -22.7}, {"time": 0.0667, "angle": -17.38}, {"time": 0.1333, "angle": -17.7}, {"time": 0.1667, "angle": -9.09}, {"time": 0.2, "angle": -9.34}, {"time": 0.2333, "angle": -3.31}, {"time": 0.2667, "angle": 0.65}, {"time": 0.3333, "angle": 5.23}, {"time": 0.3667, "angle": 17.56}, {"time": 0.5, "angle": 27.97}, {"time": 0.6333, "angle": -1.45}, {"time": 0.7, "angle": -1.78}, {"time": 0.7667, "angle": -8.9}, {"time": 0.8, "angle": -5.4}, {"time": 0.8667, "angle": -15.32}, {"time": 0.9, "angle": -9.19}, {"time": 0.9667, "angle": -23.6}, {"time": 1, "angle": -22.7}]}, "rear-upper-arm": {"rotate": [{"angle": 31.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 13.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 20.85, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 31.65}]}, "rear-bracer": {"rotate": [{"angle": 31}, {"time": 0.4333, "angle": 12.79}, {"time": 0.6667, "angle": 20.85}, {"time": 1, "angle": 31}]}, "gun": {"rotate": [{"angle": 1.95}, {"time": 0.4333, "angle": 12.79}, {"time": 0.6667, "angle": 15.87}, {"time": 1, "angle": 1.95}]}}, "transform": {"front-foot-board-transform": [{}], "rear-foot-board-transform": [{}], "toes-board": [{"translateMix": 0, "scaleMix": 0, "shearMix": 0}]}, "deform": {"default": {"eye": {"eye-indifferent": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "vertices": [0.22339, -6.575, 0.22339, -6.575, 0.22339, -6.575, 0.22339, -6.575], "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "front-foot": {"front-foot": [{"offset": 26, "vertices": [-0.02832, -5.37024, -0.02832, -5.37024, 3.8188, -3.7757, -0.02832, -5.37024, -3.82159, 3.77847]}]}, "front-shin": {"front-shin": [{"offset": 14, "vertices": [0.5298, -1.12677, -0.85507, -4.20587, -11.35158, -10.19225, -10.79865, -8.43765, -6.06447, -6.89757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.54892, -3.06021, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.80437, -7.01817]}, {"time": 0.3667, "offset": 14, "vertices": [0.5298, -1.12677, -11.66571, -9.07211, -25.65866, -17.53735, -25.53217, -16.50978, -11.78232, -11.26097, 0, 0, 0.60487, -1.63589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60487, -1.63589, 0, 0, -2.64522, -7.35739, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0.60487, -1.63589, 0.60487, -1.63589, 0.60487, -1.63589, 0.60487, -1.63589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60487, -1.63589, 0, 0, -10.06873, -12.0999]}, {"time": 0.5333, "offset": 14, "vertices": [0.5298, -1.12677, -0.85507, -4.20587, -7.00775, -8.24771, -6.45482, -6.49312, -6.06447, -6.89757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.54892, -3.06021, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.80437, -7.01817]}, {"time": 1, "offset": 14, "vertices": [0.5298, -1.12677, -0.85507, -4.20587, -11.35158, -10.19225, -10.79865, -8.43765, -6.06447, -6.89757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.54892, -3.06021, 1.48463, -2.29663, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.80437, -7.01817]}]}, "goggles": {"goggles": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "vertices": [0.67711, -3.13914, 0.27417, -1.27147, 0.15489, -0.72019, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42483, -1.97125, 1.55292, -7.20752, 0.1845, -0.85692, 0.62342, -2.89004, 0.80454, -3.72999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.01049, -4.68358, 1.14495, -5.30811, 1.05917, -4.91033, 0.7856, -3.6421, 0.88443, -4.1001, 0.91542, -4.24387, 0.80144, -3.7155, 0.7665, -3.55506, 0.29612, -1.37293, 0.03147, -0.14642, 0.22645, -1.05166, 0.13694, -0.63699, 0.25405, -1.17808, 0.55052, -2.5523, 0.77677, -3.60118, 1.59353, -7.39157, 1.35063, -6.26342, 1.34974, -6.25925, 0.94851, -4.39735, 0.83697, -3.88036, 0.80624, -3.73668, 1.01196, -4.69016, 0, 0, 0.1845, -0.85692, 0.1845, -0.85692, 0.1845, -0.85692, 0.1845, -0.85692, 0.1845, -0.85692, 0.1845, -0.85692], "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "head": {"head": [{"offset": 60, "vertices": [2.77362, 1.62589, 1.93787, 2.56528], "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "offset": 34, "vertices": [1.96774, -9.13288, 1.96774, -9.13288, 1.96774, -9.13288, 0.52141, -2.41945, 0, 0, 0, 0, 0, 0, 0, 0, -0.28486, 1.32153, -0.28486, 1.32153, 0, 0, 0, 0, 0, 0, 1.04011, 0.60971, 0.7267, 0.96198, 7.3906, -5.46259, 3.91425, 8.31534, 2.51528, -2.75824, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.35114, 5.70461, 6.83772, -5.11176, 3.67865, 7.70451, 5.75797, -8.66576, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.08572, -3.70304, 1.49945, -3.38693, 0.21432, -9.25756, 0, 0, 0, 0, 0.08572, -3.70304, 0.21432, -9.25756, 0, 0, 0.10735, -0.51047, 0.10735, -0.51047, 0.10735, -0.51047, 0.10735, -0.51047, 0.10735, -0.51047, 0.10735, -0.51047, 0.10735, -0.51047, 0.10735, -0.51047, 0.10735, -0.51047, 0, 0, 0, 0, 0, 0, 0, 0, 0.34761, -1.61296, 0.26072, -1.20974, 0.65176, -3.02431], "curve": 0.25, "c3": 0.75}, {"time": 1, "offset": 60, "vertices": [2.77362, 1.62589, 1.93787, 2.56528]}]}, "hoverboard-board": {"hoverboard-board": [{}, {"time": 0.2667, "offset": 1, "vertices": [2.45856, 0, 0, 0, 0, 0, 0, 0, 0, 3.55673, -0.0003, 3.55673, -0.0003, 0, 0, 0, 0, 0, 0, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, -0.00076, -9.84158, 0, 0, 0, 0, 0, 0, 0, 0, -4.90558, 0.11214, -9.40706, 0.00062, -6.34871, 0.00043, -6.34925, -6.57018, -6.34925, -6.57018, -6.34871, 0.00043, -2.3308, 0.00017, -2.33133, -6.57045, -2.33133, -6.57045, -2.3308, 0.00017, 0, 0, 0.00012, 2.45856, 0.00012, 2.45856, 0.00012, 2.45856, 0.00012, 2.45856, 3.3297, 4.44005, 3.3297, 4.44005, 3.3297, 4.44005, 0.00012, 2.45856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.46227, 0.00017, -2.46227, 0.00017, -2.52316, 1.1313, -2.52316, 1.1313, -2.52316, 1.1313, 0.00012, 2.45856, 0.00012, 2.45856, -9.40694, 2.45918, 1.88063, 0.44197, -0.00029, -3.54808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.52316, 1.1313, -2.52316, 1.1313, -2.52316, 1.1313, -2.46227, 0.00017, -2.46227, 0.00017, -2.46227, 0.00017, 0, 0, 0, 0, 0.00012, 2.45856]}, {"time": 1}]}, "mouth": {"mouth-smile": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "vertices": [0.15454, -6.6912, 0.15454, -6.6912, 0.15454, -6.6912, 0.15454, -6.6912], "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "rear-foot": {"rear-foot": [{"offset": 28, "vertices": [-1.93078, 1.34782, -0.31417, 2.33363, 3.05122, 0.33946, 2.31472, -2.01678, 2.17583, -2.05795, -0.04277, -2.99459, 1.15429, 0.26328, 0.97501, -0.67169]}]}, "torso": {"torso": [{}, {"time": 0.2667, "offset": 10, "vertices": [4.46481, -0.3543, 4.46481, -0.35429, 4.46481, -0.3543, 4.46481, -0.35429, 4.46481, -0.3543, 4.46481, -0.35429, 4.46481, -0.3543, 0, 0, -0.59544, -7.5094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 3e-05, -1e-05, -0.5954, -7.50941, -0.5954, -7.50941, -0.5954, -7.50941, -0.5954, -7.50941, 3.86934, -7.86369, 3.86935, -7.86369, 3.86934, -7.86369, 3.86935, -7.86369, -0.5954, -7.50941, -0.5954, -7.50941, -0.5954, -7.50941, -0.5954, -7.50941, -0.59544, -7.5094, -0.5954, -7.50941, -0.59544, -7.5094, -0.5954, -7.50941, 3e-05, -1e-05, 0.35948, -1.81172, 0, 0, 0, 0, -0.13678, -6.00883, -0.13666, -6.0088, 2.46274, -6.26834, 2.27113, -5.86305, 2.27148, -5.86322, 0.52808, -3.21825]}, {"time": 0.5}, {"time": 0.6333, "offset": 2, "vertices": [3.41785, -0.27124, 3.41788, -0.27125, 3.41785, -0.27124, 3.41788, -0.27125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4682, 5.90338, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 3e-05, -1e-05, 3.88608, 5.63213, 3.88608, 5.63213, 0.46823, 5.90337, 0.46823, 5.90337, 0, 0, 0, 0, 0.4682, 5.90338, 0.46823, 5.90337, 0.46823, 5.90337, 0.46823, 5.90337, 0.46823, 5.90337, 0.46823, 5.90337, 0.4682, 5.90338, 0.46823, 5.90337, 0.4682, 5.90338, 0.46823, 5.90337, 3e-05, -1e-05, 0, 0, 0, 0, 0, 0, -0.5545, 7.37883, -0.5545, 7.37883, -0.26138, 7.75283, -0.76694, 6.33778, -0.76703, 6.33779]}, {"time": 1}]}}}}, "idle": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}]}}, "bones": {"front-foot-target": {"translate": [{"x": -69.06}]}, "hip": {"translate": [{"x": -7.16, "y": -23.15, "curve": 0.205, "c3": 0.75}, {"time": 0.6667, "x": -5.33, "y": -35.48, "curve": 0.591, "c3": 0.642}, {"time": 1.6667, "x": -7.16, "y": -23.15}]}, "rear-foot-target": {"translate": [{"x": 48.87}]}, "front-upper-arm": {"rotate": [{"angle": -70.59}, {"time": 0.8, "angle": -80.61}, {"time": 1.6667, "angle": -70.59}]}, "front-bracer": {"rotate": [{"angle": 42.09}]}, "rear-upper-arm": {"rotate": [{"angle": 39.2}, {"time": 0.6667, "angle": 29.37}, {"time": 1.6667, "angle": 39.2}]}, "head": {"rotate": [{"angle": -8.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.12, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.95}]}, "front-fist": {"rotate": [{}, {"time": 0.8, "angle": 2.04}, {"time": 1.6667}], "scale": [{}, {"time": 0.8, "x": 0.94}, {"time": 1.6667}]}, "rear-bracer": {"rotate": [{}, {"time": 0.6667, "angle": 16.09}, {"time": 1.6667}]}, "gun": {"rotate": [{}, {"time": 0.6667, "angle": 0.45}, {"time": 1.6667}]}, "torso": {"rotate": [{"angle": -8.85}, {"time": 0.6667, "angle": -13.61}, {"time": 1.6667, "angle": -8.85}]}, "neck": {"rotate": [{"angle": 3.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.45, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.78}]}}}, "idle-turn": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}]}}, "bones": {"front-upper-arm": {"rotate": [{"angle": -302.77, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.2667, "angle": -70.59}], "translate": [{"x": -5.24, "y": -18.27, "curve": 0.25, "c3": 0.418}, {"time": 0.2667}]}, "rear-upper-arm": {"rotate": [{"angle": 248.56, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.1333, "angle": 39.2}], "translate": [{"x": -2.84, "y": 37.28, "curve": 0.25, "c3": 0.521}, {"time": 0.1333}]}, "gun": {"rotate": [{"angle": -3.95, "curve": 0, "c2": 0.39, "c3": 0.354, "c4": 0.72}, {"time": 0.0333, "angle": -20.45, "curve": 0.288, "c2": 0.75, "c3": 0.55}, {"time": 0.2}]}, "neck": {"rotate": [{"angle": 17.2, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.2667, "angle": 3.78}]}, "hip": {"translate": [{"x": -2.69, "y": -6.79, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.2667, "x": -7.16, "y": -23.15}]}, "front-fist": {"rotate": [{"angle": -15.54, "curve": 0, "c2": 0.36, "c3": 0.343, "c4": 0.69}, {"time": 0.0667, "angle": 19.02, "curve": 0.082, "c2": 0.81, "c3": 0.514}, {"time": 0.2667}], "scale": [{"x": 0.94, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.2667}]}, "rear-bracer": {"rotate": [{"angle": 11.75, "curve": 0, "c2": 0.44, "c3": 0.369, "c4": 0.76}, {"time": 0.0333, "angle": -33.39, "curve": 0.207, "c2": 0.78, "c3": 0.587}, {"time": 0.2}]}, "torso": {"rotate": [{"angle": -18.25, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.2667, "angle": -8.85}], "scale": [{"y": 1.03, "curve": 0.25, "c3": 0.494}, {"time": 0.2667}]}, "head": {"rotate": [{"angle": 5.12, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.2667, "angle": -8.95}], "scale": [{"y": 1.03, "curve": 0.25, "c3": 0.401}, {"time": 0.2667}]}, "rear-foot-target": {"translate": [{"x": -58.39, "y": 30.48, "curve": 0, "c2": 0.55, "c3": 0.403, "c4": 0.85}, {"time": 0.1, "x": 34.14, "y": -1.61, "curve": 0.286, "c2": 0.75, "c3": 0.634}, {"time": 0.2, "x": 48.87}]}, "front-bracer": {"rotate": [{"angle": 6.69, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.2667, "angle": 42.09}]}, "front-foot-target": {"rotate": [{"angle": -1.85}, {"time": 0.1667}], "translate": [{"x": 9.97, "y": 0.82, "curve": 0, "c2": 0.81, "c3": 0.467}, {"time": 0.1667, "x": -69.06}]}, "hair3": {"rotate": [{"angle": -9.01, "curve": 0.25, "c3": 0.361}, {"time": 0.2667}]}, "hair4": {"rotate": [{"angle": -16.49, "curve": 0.25, "c3": 0.361}, {"time": 0.2667}]}, "hair1": {"rotate": [{"angle": -3.85, "curve": 0.25, "c3": 0.361}, {"time": 0.2667}]}, "hair2": {"rotate": [{"angle": 1.25, "curve": 0.25, "c3": 0.361}, {"time": 0.2667}]}, "front-thigh": {"translate": [{"x": 12.21, "y": 1.89, "curve": 0.25, "c3": 0.75}, {"time": 0.1333}]}, "rear-thigh": {"translate": [{"x": -16.11, "y": -1.38, "curve": 0.25, "c3": 0.75}, {"time": 0.1333}]}}, "deform": {"default": {"torso": {"torso": [{"offset": 2, "vertices": [4.71576, 4.44464, 4.71579, 4.44463, 4.7399, 4.67474, 4.73993, 4.67473, 5.0968, 8.08033, 5.0968, 8.08034, 5.1181, 8.28423, 5.11813, 8.28422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 3e-05, -1e-05, 1.21198, -8.88572, 1.21201, -8.88573, 1.2106, -7.18206, 1.21063, -7.18207, 0.98038, -5.14252, 0.98038, -5.14252, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, -1.13269, -8.03748, -1.13266, -8.03748, -1.13268, -8.03748, -1.13269, -8.03748, -1.13268, -8.03748, -1.13266, -8.03748, 3e-05, -1e-05, 0, 0, 0, 0, 0, 0, 0.77191, -5.83292, 0.77274, -5.83294, 0, 0, 0.67996, -9.11016, 0.67938, -9.11015], "curve": 0.25, "c3": 0.282}, {"time": 0.2667, "offset": 74, "vertices": [0.52324, 5.68796, 0.52335, 5.68797, 0.52335, 5.68797, 0.52347, 5.68797, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 0.49251, 5.35334, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0.52335, 5.68797, 0, 0, 0, 0, 2.59232, 6.1724]}]}}}}, "jump": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}, {"time": 0.2, "name": "front-fist-closed"}, {"time": 0.6667, "name": "front-fist-open"}]}, "mouth": {"attachment": [{"name": "mouth-grind"}]}}, "bones": {"front-thigh": {"rotate": [{"angle": 91.53, "curve": 0.278, "c2": 0.46, "c3": 0.764}, {"time": 0.2, "angle": -35.84, "curve": 0.761, "c3": 0.75}, {"time": 0.4333, "angle": 127.74}, {"time": 0.7333, "angle": 48.18, "curve": 0.227, "c2": 0.27, "c3": 0.433}, {"time": 0.8333, "angle": 25.35}, {"time": 0.9333, "angle": 45.38}, {"time": 1.0333, "angle": 38.12}, {"time": 1.1333, "angle": 25.35}, {"time": 1.3333, "angle": 91.53}], "translate": [{"x": -2.57, "y": 5.78}, {"time": 0.4333, "x": 8.3, "y": 7.99}, {"time": 0.7333, "x": 7.21, "y": -4}, {"time": 1.3333, "x": -2.57, "y": 5.78}]}, "torso": {"rotate": [{"angle": -42.64}, {"time": 0.2, "angle": -5.74}, {"time": 0.4333, "angle": -50.76}, {"time": 0.7333, "angle": 1.9}, {"time": 0.8333, "angle": 11.59}, {"time": 0.9667, "angle": -1.9}, {"time": 1.1333, "angle": 11.59}, {"time": 1.3333, "angle": -42.64}]}, "rear-thigh": {"rotate": [{"angle": -26.32}, {"time": 0.2, "angle": 121.44}, {"time": 0.4333, "angle": 70.55}, {"time": 0.7333, "angle": 79.9, "curve": 0.296, "c2": 0.3, "c3": 0.59}, {"time": 0.8333, "angle": 99.12}, {"time": 0.9333, "angle": 74.06}, {"time": 1.0333, "angle": 98.05}, {"time": 1.1333, "angle": 99.12}, {"time": 1.3333, "angle": -26.32}], "translate": [{"x": -0.56, "y": -0.32}, {"time": 0.4333, "x": -8.5, "y": 10.58}, {"time": 0.7333, "x": -1.96, "y": -0.32}, {"time": 1.3333, "x": -0.56, "y": -0.32}]}, "rear-shin": {"rotate": [{"angle": -78.69}, {"time": 0.4333, "angle": -55.56}, {"time": 0.7333, "angle": -62.84}, {"time": 0.8333, "angle": -80.75}, {"time": 0.9333, "angle": -41.13}, {"time": 1.0333, "angle": -77.4}, {"time": 1.1333, "angle": -80.75}, {"time": 1.3333, "angle": -78.69}]}, "front-upper-arm": {"rotate": [{"angle": -22.62}, {"time": 0.2, "angle": -246.69}, {"time": 0.6, "angle": 11.28, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7333, "angle": -57.46, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.8667, "angle": -112.6}, {"time": 0.9333, "angle": -102.17}, {"time": 1.0333, "angle": -108.61}, {"time": 1.1333, "angle": -112.6}, {"time": 1.3333, "angle": -22.62}], "translate": [{"x": 6.08, "y": 7.15}, {"time": 0.2, "x": 7.23, "y": -13.13, "curve": "stepped"}, {"time": 0.7333, "x": 7.23, "y": -13.13}, {"time": 1.3333, "x": 6.08, "y": 7.15}]}, "front-bracer": {"rotate": [{"angle": 66.47}, {"time": 0.2, "angle": 42.4}, {"time": 0.4333, "angle": 26.06}, {"time": 0.7333, "angle": 13.28}, {"time": 0.8667, "angle": -28.65}, {"time": 0.9333, "angle": -22.31}, {"time": 1.0333, "angle": -35.39}, {"time": 1.1333, "angle": -28.65}, {"time": 1.3333, "angle": 66.47}]}, "front-fist": {"rotate": [{"angle": -28.43}, {"time": 0.4333, "angle": -45.61}, {"time": 0.7333, "angle": -53.66}, {"time": 0.8667, "angle": 7.56}, {"time": 0.9333, "angle": 31.16}, {"time": 1.0333, "angle": -32.59}, {"time": 1.1333, "angle": 7.56}, {"time": 1.3333, "angle": -28.43}]}, "rear-upper-arm": {"rotate": [{"angle": 39.69}, {"time": 0.2, "angle": 276.58}, {"time": 0.3, "angle": 17.74}, {"time": 0.4333, "angle": 83.38}, {"time": 0.6, "angle": -4.72, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7333, "angle": -69.63, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.7667, "angle": 321.47, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.8, "angle": 33.71, "curve": 0.359, "c2": 0.64, "c3": 0.694}, {"time": 0.8667, "angle": 34.56}, {"time": 1.0333, "angle": 71.97}, {"time": 1.1333, "angle": 34.56}, {"time": 1.3333, "angle": 39.69}], "translate": [{"x": -3.1, "y": -4.87}, {"time": 0.2, "x": 23.33, "y": 49.07}, {"time": 0.4333, "x": 20.78, "y": 40.21}, {"time": 1.3333, "x": -3.1, "y": -4.87}]}, "rear-bracer": {"rotate": [{"angle": 29.67}, {"time": 0.2, "angle": 45.07}, {"time": 0.4333, "angle": -4.35}, {"time": 0.7667, "angle": 61.69}, {"time": 0.8, "angle": 82.6}, {"time": 0.8667, "angle": 80.06}, {"time": 1.0333, "angle": 57.56}, {"time": 1.1333, "angle": 80.06}, {"time": 1.3333, "angle": 29.67}]}, "neck": {"rotate": [{"angle": 24.91}, {"time": 0.2, "angle": 16.32}, {"time": 0.4333, "angle": 7.45}, {"time": 0.7333, "angle": -20.35}, {"time": 0.8333, "angle": -0.69, "curve": "stepped"}, {"time": 1.1333, "angle": -0.69}, {"time": 1.3333, "angle": 24.91}]}, "head": {"rotate": [{"angle": 24.92}, {"time": 0.2, "angle": 10.36}, {"time": 0.4333, "angle": 28.65}, {"time": 0.7333, "angle": -2.66}, {"time": 0.8333, "angle": -28.94, "curve": "stepped"}, {"time": 1.1333, "angle": -28.94}, {"time": 1.3333, "angle": 24.92}]}, "hip": {"translate": [{"x": -34.52, "y": -78.63, "curve": 0.233, "c2": 1.01, "c3": 0.75}, {"time": 0.2, "x": -34.52, "y": 182.51, "curve": 0.232, "c2": 0.48, "c3": 0.599, "c4": 0.79}, {"time": 0.7667, "x": -34.52, "y": 596.22, "curve": 0.33, "c2": 0.17, "c3": 0.661, "c4": 0.22}, {"time": 1.1333, "x": -34.52, "y": 2.5}, {"time": 1.3333, "x": -34.52, "y": -78.63}]}, "front-shin": {"rotate": [{"angle": -90.63, "curve": 0.416, "c2": 0.55, "c3": 0.743}, {"time": 0.2, "angle": -10.52, "curve": 0.644, "c2": 0.01, "c3": 0.75}, {"time": 0.4333, "angle": -127.72}, {"time": 0.7333, "angle": -19.92}, {"time": 0.8333, "angle": -5.17}, {"time": 0.9333, "angle": -35.06}, {"time": 1.0333, "angle": -43.97}, {"time": 1.1333, "angle": -5.17}, {"time": 1.3333, "angle": -90.63}]}, "front-foot": {"rotate": [{"angle": -0.8}, {"time": 0.0333, "angle": 16.28}, {"time": 0.0667, "angle": 23.52}, {"time": 0.1, "angle": 21.02}, {"time": 0.1333, "angle": 10.93}, {"time": 0.2, "angle": -38.46}, {"time": 0.4333, "angle": 6.62}, {"time": 0.7333, "angle": -11.52}, {"time": 1.0333, "angle": -22.92}, {"time": 1.3333, "angle": -0.8}]}, "rear-foot": {"rotate": [{"angle": -12.78}, {"time": 0.2, "angle": 17.06}, {"time": 0.4333, "angle": 19.45}, {"time": 0.7333, "angle": 2.67}, {"time": 1.0333, "angle": -28.5}, {"time": 1.3333, "angle": -12.78}]}, "gun": {"rotate": [{"angle": 6.18}, {"time": 0.2, "angle": 30.81}, {"time": 0.4333, "angle": 13.26}, {"time": 0.7333, "angle": 14.98}, {"time": 0.7667, "angle": 25.65}, {"time": 0.8, "angle": 20.62}, {"time": 0.8667, "angle": 64.53}, {"time": 1.0333, "angle": 8.6}, {"time": 1.1333, "angle": 64.53}, {"time": 1.3333, "angle": 6.18}]}, "back-foot-tip": {"rotate": [{"angle": -134.56}, {"time": 0.0667, "angle": -53.37}, {"time": 0.1667, "angle": 44.6}, {"time": 0.4333, "angle": 20.16}, {"time": 0.7333, "angle": 27.1}, {"time": 0.9667, "angle": 22.88}, {"time": 1.2667, "angle": -35.32}, {"time": 1.3333, "angle": -134.56}]}, "front-foot-tip": {"rotate": [{}, {"time": 0.1667, "angle": -52.5}, {"time": 0.4333, "angle": -15.64}, {"time": 0.7333, "angle": 25.35}, {"time": 0.9667, "angle": -21.32}, {"time": 1.1333, "angle": -10.35}, {"time": 1.2, "angle": 0.81}]}, "hair3": {"rotate": [{"angle": 22.53}, {"time": 0.0667, "angle": 11.66}, {"time": 0.2, "angle": -6.59}, {"time": 0.6667, "angle": 9.32}, {"time": 1.3333, "angle": 22.53}]}, "hair4": {"rotate": [{"angle": -6.07}, {"time": 0.0667, "angle": 11.67}, {"time": 0.2, "angle": -6.57}, {"time": 0.3333, "angle": 10.17}, {"time": 0.6667, "angle": 14.76}, {"time": 0.8667, "angle": -33.44}, {"time": 1.1667, "angle": -19.29}, {"time": 1.3333, "angle": -6.07}]}, "hair2": {"rotate": [{"angle": 2.7}, {"time": 0.0667, "angle": 11.67}, {"time": 0.2, "angle": -6.57}, {"time": 0.3333, "angle": 18.94}, {"time": 0.6667, "angle": 23.53}, {"time": 0.8667, "angle": -24.67}, {"time": 1.1667, "angle": -10.51}, {"time": 1.3333, "angle": 2.7}]}, "hair1": {"rotate": [{"angle": 22.54}, {"time": 0.0667, "angle": 11.67}, {"time": 0.2, "angle": -6.57}, {"time": 0.6667, "angle": 9.33}, {"time": 1.3333, "angle": 22.54}]}}, "ik": {"front-foot-ik": [{"mix": 0}], "front-leg-ik": [{"mix": 0, "bendPositive": false}], "rear-foot-ik": [{"mix": 0}], "rear-leg-ik": [{"mix": 0, "bendPositive": false}]}, "events": [{"time": 1.1333, "name": "footstep"}]}, "portal": {"slots": {"clipping": {"attachment": [{"name": "clipping"}]}, "front-fist": {"attachment": [{"name": "front-fist-open"}]}, "portal-bg": {"attachment": [{"name": "portal-bg"}, {"time": 3.1, "name": null}]}, "portal-flare1": {"attachment": [{"time": 1.1, "name": "portal-flare1"}, {"time": 1.1333, "name": "portal-flare2"}, {"time": 1.1667, "name": "portal-flare3"}, {"time": 1.2, "name": "portal-flare1"}, {"time": 1.2333, "name": "portal-flare2"}, {"time": 1.2667, "name": "portal-flare1"}, {"time": 1.3333, "name": null}]}, "portal-flare2": {"attachment": [{"time": 1.1, "name": "portal-flare2"}, {"time": 1.1333, "name": "portal-flare3"}, {"time": 1.1667, "name": "portal-flare1"}, {"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare3"}, {"time": 1.2667, "name": null}]}, "portal-flare3": {"attachment": [{"time": 1.2, "name": "portal-flare3"}, {"time": 1.2333, "name": "portal-flare2"}, {"time": 1.2667, "name": null}]}, "portal-flare4": {"attachment": [{"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare1"}, {"time": 1.2667, "name": "portal-flare2"}, {"time": 1.3333, "name": null}]}, "portal-flare5": {"attachment": [{"time": 1.2333, "name": "portal-flare3"}, {"time": 1.2667, "name": "portal-flare1"}, {"time": 1.3333, "name": null}]}, "portal-flare6": {"attachment": [{"time": 1.2667, "name": "portal-flare3"}, {"time": 1.3333, "name": null}]}, "portal-flare7": {"attachment": [{"time": 1.1333, "name": "portal-flare2"}, {"time": 1.1667, "name": null}]}, "portal-flare8": {"attachment": [{"time": 1.2, "name": "portal-flare3"}, {"time": 1.2333, "name": "portal-flare2"}, {"time": 1.2667, "name": null}]}, "portal-flare9": {"attachment": [{"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare3"}, {"time": 1.2667, "name": "portal-flare1"}, {"time": 1.3, "name": null}]}, "portal-flare10": {"attachment": [{"time": 1.2, "name": "portal-flare2"}, {"time": 1.2333, "name": "portal-flare1"}, {"time": 1.2667, "name": "portal-flare3"}, {"time": 1.3, "name": null}]}, "portal-shade": {"attachment": [{"name": "portal-shade"}, {"time": 3.1, "name": null}]}, "portal-streaks1": {"attachment": [{"name": "portal-streaks1"}, {"time": 3.1, "name": null}]}, "portal-streaks2": {"attachment": [{"name": "portal-streaks2"}, {"time": 3.1, "name": null}]}}, "bones": {"portal-root": {"translate": [{"x": -458.35, "y": 105.19, "curve": 0.934, "c2": 0.07, "c3": 0.671, "c4": 0.99}, {"time": 1, "x": -448.03, "y": 105.19}, {"time": 2.5, "x": -431.97, "y": 105.19, "curve": 0.426, "c3": 0.747, "c4": 0.41}, {"time": 3.1, "x": -457.42, "y": 105.19}], "scale": [{"x": 0.003, "y": 0.006, "curve": 0.823, "c2": 0.24, "c3": 0.867, "c4": 0.66}, {"time": 0.4, "x": 0.175, "y": 0.387, "curve": 0.727, "c2": 1.8, "c3": 0.671, "c4": 0.99}, {"time": 1, "x": 0.645, "y": 1.426}, {"time": 1.2333, "x": 0.685, "y": 1.516}, {"time": 1.6, "x": 0.634, "y": 1.401}, {"time": 1.9667, "x": 0.67, "y": 1.481}, {"time": 2.2, "x": 0.688, "y": 1.522}, {"time": 2.5, "x": 0.645, "y": 1.426, "curve": 0.98, "c2": -0.26, "c3": 0.717}, {"time": 3.1, "x": 0.007, "y": 0.015}]}, "portal-streaks1": {"rotate": [{}, {"time": 0.3333, "angle": 120}, {"time": 0.6667, "angle": -120}, {"time": 1}, {"time": 1.3333, "angle": 120}, {"time": 1.6667, "angle": -120}, {"time": 2}, {"time": 2.3333, "angle": 120}, {"time": 2.6667, "angle": -120}, {"time": 3}, {"time": 3.3333, "angle": 120}], "translate": [{"x": 15.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6667, "x": 10.9, "y": -6.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1, "x": 9.21, "y": -8.66}, {"time": 1.3333, "x": 21.53, "y": -3.19}, {"time": 2, "x": 9.21, "y": 6.26}, {"time": 2.5667, "x": 9.21, "y": -0.8}, {"time": 2.9333, "x": 9.21, "y": -8.91}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.053, "y": 1.053, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.986, "y": 0.986, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.053, "y": 1.053}]}, "portal-streaks2": {"rotate": [{}, {"time": 0.6667, "angle": 120}, {"time": 1.3333, "angle": -120}, {"time": 2}, {"time": 2.6667, "angle": 120}, {"time": 3.3333, "angle": -120}], "translate": [{"x": -2.11}, {"time": 1, "x": -2.11, "y": 6.63}, {"time": 1.9333, "x": -2.11}], "scale": [{"x": 1.014, "y": 1.014}]}, "portal-shade": {"translate": [{"x": -29.68}], "scale": [{"x": 0.714, "y": 0.714}]}, "portal": {"rotate": [{}, {"time": 0.6667, "angle": 120}, {"time": 1.3333, "angle": -120}, {"time": 2}, {"time": 2.6667, "angle": 120}, {"time": 3.3333, "angle": -120}]}, "clipping": {"translate": [{"x": -476.55, "y": 2.27}], "scale": [{"x": 0.983, "y": 1.197}]}, "hip": {"rotate": [{"time": 1.0667, "angle": 22.74}], "translate": [{"x": -899.41, "y": 4.47, "curve": "stepped"}, {"time": 1.0667, "x": -694.16, "y": 183.28}, {"time": 1.1333, "x": -509.15, "y": 83.28}, {"time": 1.2333, "x": -316.97, "y": 37.07}, {"time": 1.4, "x": -160.9, "y": -90.39}, {"time": 1.6, "x": -102.86, "y": -94.33, "curve": 0.596, "c2": 0.01, "c3": 0.75}, {"time": 2.1333, "x": -7.2, "y": -31.12, "curve": 0.205, "c3": 0.75}, {"time": 2.6, "x": -5.34, "y": -36.81, "curve": 0.591, "c3": 0.642}, {"time": 3.6, "x": -7.16, "y": -24.48}]}, "rear-foot-target": {"rotate": [{"time": 1.0667, "angle": 41.6, "curve": "stepped"}, {"time": 1.2333, "angle": 41.6}, {"time": 1.3333, "angle": 20.8}, {"time": 1.4, "angle": 19.02}, {"time": 1.4333, "angle": -0.28}], "translate": [{"x": -899.41, "y": 4.47, "curve": "stepped"}, {"time": 1.0667, "x": -591.13, "y": 438.46}, {"time": 1.1333, "x": -406.12, "y": 338.47}, {"time": 1.2333, "x": -214.35, "y": 255.24}, {"time": 1.4, "x": -8.88, "y": 15.25}, {"time": 1.4333, "x": 8.36, "y": 0.2, "curve": 0.216, "c2": 0.54, "c3": 0.75}, {"time": 1.9333, "x": 48.87}]}, "front-foot-target": {"rotate": [{"time": 1.0667, "angle": 32.08, "curve": "stepped"}, {"time": 1.2333, "angle": 32.08}, {"time": 1.3333, "angle": -0.28}, {"time": 1.6, "angle": -34.77}, {"time": 1.9333, "angle": -2.15}], "translate": [{"x": -899.41, "y": 4.47, "curve": "stepped"}, {"time": 1.0667, "x": -533.93, "y": 363.75}, {"time": 1.1333, "x": -348.92, "y": 263.76}, {"time": 1.2333, "x": -201.23, "y": 199.93}, {"time": 1.3333, "x": -109.57, "y": 0.2, "curve": 0.255, "c2": 0.48, "c3": 0.75}, {"time": 1.7333, "x": -69.06}]}, "torso": {"rotate": [{"time": 1.0667, "angle": 9.73, "curve": "stepped"}, {"time": 1.2333, "angle": 9.73}, {"time": 1.3333, "angle": 2.88}, {"time": 1.4667, "angle": -73.99}, {"time": 1.6, "angle": -75.07, "curve": 0.392, "c2": 0.03, "c3": 0.719, "c4": 0.43}, {"time": 1.7333, "angle": -77.34, "curve": 0.456, "c2": 0.36, "c3": 0.68, "c4": 1.21}, {"time": 2.3333, "angle": -32.03}, {"time": 2.6, "angle": -36.79}, {"time": 3.6, "angle": -32.03}]}, "neck": {"rotate": [{"time": 1.0667, "angle": -3.57, "curve": "stepped"}, {"time": 1.1333, "angle": -3.57}, {"time": 1.2333, "angle": -13.5}, {"time": 1.3333, "angle": -1.7}, {"time": 1.4333, "angle": 2.3}, {"time": 1.5667, "angle": 11.42}, {"time": 1.9333, "angle": 3.78, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.1333, "angle": 7.93, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.6, "angle": 5.45, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 3.78}]}, "head": {"rotate": [{"time": 1.0667, "angle": 16.4, "curve": "stepped"}, {"time": 1.1333, "angle": 16.4}, {"time": 1.2333, "angle": 15.19}, {"time": 1.3333, "angle": -32.21}, {"time": 1.4333, "angle": 15.95}, {"time": 1.5667, "angle": 20.28}, {"time": 1.7333, "angle": 15.24}, {"time": 1.9333, "angle": -18.95, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2.1333, "angle": 2.65, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.6, "angle": -4.12, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -8.95}]}, "rear-upper-arm": {"rotate": [{"time": 1.0667, "angle": 330.49, "curve": "stepped"}, {"time": 1.1333, "angle": 330.49}, {"time": 1.2333, "angle": 21.94}, {"time": 1.4, "angle": 8.14}, {"time": 1.8, "angle": -3.47, "curve": 0.673, "c2": 0.01, "c3": 0.747, "c4": 0.98}, {"time": 2, "angle": 39.2}, {"time": 2.8333, "angle": 31.41, "curve": 0.322, "c2": 0.17, "c3": 0.655, "c4": 0.5}, {"time": 3.6, "angle": 39.2}]}, "back-foot-tip": {"rotate": [{"time": 1.0667, "angle": 56.07, "curve": "stepped"}, {"time": 1.1333, "angle": 56.07}, {"time": 1.2333, "angle": 24.68}, {"time": 1.3667, "angle": 30.41}, {"time": 1.4333, "angle": 19.18}, {"time": 1.5, "angle": -0.84}]}, "front-upper-arm": {"rotate": [{"time": 1.0667, "angle": -239.74, "curve": "stepped"}, {"time": 1.1333, "angle": -239.74}, {"time": 1.2333, "angle": -287.2}, {"time": 1.3333, "angle": -28.87}, {"time": 1.4667, "angle": -92.44}, {"time": 1.9333, "angle": -80.61}, {"time": 3.6, "angle": -70.59}]}, "front-bracer": {"rotate": [{"time": 1.0667, "angle": 0.66, "curve": "stepped"}, {"time": 1.2333, "angle": 0.66}, {"time": 1.3333, "angle": 36.83}, {"time": 1.4333, "angle": 12}, {"time": 1.5, "angle": -10.19}, {"time": 1.5667, "angle": -8}, {"time": 1.9333, "angle": 42.09}]}, "front-thigh": {"translate": [{"time": 1.1, "x": -6.41, "y": 18.23, "curve": "stepped"}, {"time": 1.1333, "x": -6.41, "y": 18.23}, {"time": 1.2, "x": 1.61, "y": 3.66}, {"time": 1.2333, "x": 4.5, "y": -3.15}, {"time": 1.3667, "x": -3.79, "y": 2.94}, {"time": 1.4, "x": -8.37, "y": 8.72}, {"time": 1.4333, "x": -11.26, "y": 16.99}, {"time": 1.4667, "x": -9.89, "y": 24.73, "curve": "stepped"}, {"time": 1.8667, "x": -9.89, "y": 24.73}, {"time": 2.1, "x": -4.66, "y": 10.25}]}, "front-foot-tip": {"rotate": [{"time": 1.0667, "angle": 42.55, "curve": "stepped"}, {"time": 1.1333, "angle": 42.55}, {"time": 1.2333, "angle": 17.71}, {"time": 1.3667, "angle": 3.63}, {"time": 1.4333, "angle": 1.45}]}, "rear-bracer": {"rotate": [{"time": 1.0667, "angle": 108.71, "curve": "stepped"}, {"time": 1.1333, "angle": 108.71}, {"time": 1.2333, "angle": 64.64}, {"time": 1.4, "angle": 66.25}, {"time": 1.7, "angle": 26.39}, {"time": 1.8, "angle": 13.42}, {"time": 2}, {"time": 2.8333, "angle": 11.32}, {"time": 3.6}]}, "front-fist": {"rotate": [{"time": 1.1, "angle": 6.32}, {"time": 1.2}, {"time": 1.4667, "angle": 24.51}, {"time": 1.5667, "angle": -6.03}, {"time": 1.7, "angle": -44.92}, {"time": 1.9333}, {"time": 2.7333, "angle": 2.04}, {"time": 3.6}], "scale": [{"time": 1.9333}, {"time": 2.7333, "x": 0.844}, {"time": 3.6}]}, "gun": {"rotate": [{"time": 1.2667}, {"time": 1.7, "angle": 17.34}, {"time": 1.8, "angle": 21.99}, {"time": 2}, {"time": 2.8333, "angle": 6.53}, {"time": 3.6}]}, "hair2": {"rotate": [{"time": 1.0667, "angle": 26.19, "curve": "stepped"}, {"time": 1.4333, "angle": 26.19}, {"time": 1.5667, "angle": -16.41, "curve": 0.664, "c3": 0.75}, {"time": 1.7, "angle": 7.44}, {"time": 1.8, "angle": 22.84}, {"time": 2, "angle": 35.35}, {"time": 2.1, "angle": 19.51}, {"time": 2.1667}]}, "hair4": {"rotate": [{"time": 1.0667, "angle": 26.19, "curve": "stepped"}, {"time": 1.4333, "angle": 26.19}, {"time": 1.5667, "angle": -16.41, "curve": 0.664, "c3": 0.75}, {"time": 1.7, "angle": 7.44}, {"time": 1.8, "angle": 22.84}, {"time": 2, "angle": 35.35}, {"time": 2.1, "angle": 19.51}, {"time": 2.1667}]}, "hair3": {"rotate": [{"time": 1.4333}, {"time": 1.5667, "angle": -8.68, "curve": 0.664, "c3": 0.75}, {"time": 1.7}]}, "hair1": {"rotate": [{"time": 1.4333}, {"time": 1.5667, "angle": -8.68, "curve": 0.664, "c3": 0.75}, {"time": 1.7}]}, "flare1": {"rotate": [{"time": 1.1, "angle": 8.2}], "translate": [{"time": 1.1, "x": -19.97, "y": 149.68}, {"time": 1.2, "x": 3.85, "y": 152.43}, {"time": 1.2333, "x": -15.42, "y": 152.29}], "scale": [{"time": 1.1, "x": 0.805, "y": 0.805}, {"time": 1.1667, "x": 1.279, "y": 0.605}, {"time": 1.2, "x": 2.151, "y": 0.805}, {"time": 1.2333, "x": 1.608, "y": 0.805}, {"time": 1.3, "x": 0.547, "y": 0.416}], "shear": [{"time": 1.1, "y": 4.63}, {"time": 1.2333, "x": -5.74, "y": 4.63}]}, "flare2": {"rotate": [{"time": 1.1, "angle": 12.29}], "translate": [{"time": 1.1, "x": -8.63, "y": 132.96}, {"time": 1.2, "x": 4.35, "y": 132.93}], "scale": [{"time": 1.1, "x": 0.864, "y": 0.864}, {"time": 1.1667, "x": 0.945, "y": 0.945}, {"time": 1.2, "x": 1.511, "y": 1.081}], "shear": [{"time": 1.1, "y": 24.03}]}, "flare3": {"rotate": [{"time": 1.1667, "angle": 2.88}], "translate": [{"time": 1.1667, "x": 3.24, "y": 114.81}], "scale": [{"time": 1.1667, "x": 0.668, "y": 0.668}], "shear": [{"time": 1.1667, "y": 38.59}]}, "flare4": {"rotate": [{"time": 1.1667, "angle": -8.64}], "translate": [{"time": 1.1667, "x": -3.82, "y": 194.06}, {"time": 1.2667, "x": -1.82, "y": 198.47, "curve": "stepped"}, {"time": 1.3, "x": -1.94, "y": 187.81}], "scale": [{"time": 1.1667, "x": 0.545, "y": 0.545}, {"time": 1.2667, "x": 0.757, "y": 0.757}], "shear": [{"time": 1.1667, "x": 7.42, "y": -22.04}]}, "flare5": {"translate": [{"time": 1.2, "x": -11.17, "y": 176.42}, {"time": 1.2333, "x": -8.56, "y": 179.04, "curve": "stepped"}, {"time": 1.3, "x": -14.57, "y": 168.69}], "scale": [{"time": 1.2333, "x": 1.146}, {"time": 1.3, "x": 0.703, "y": 0.61}], "shear": [{"time": 1.2, "x": 6.9}]}, "flare6": {"rotate": [{"time": 1.2333, "angle": -5.36}, {"time": 1.2667, "angle": -0.54}], "translate": [{"time": 1.2333, "x": 14.52, "y": 204.67}, {"time": 1.2667, "x": 19.16, "y": 212.9, "curve": "stepped"}, {"time": 1.3, "x": 9.23, "y": 202.85}], "scale": [{"time": 1.2333, "x": 0.777, "y": 0.49}, {"time": 1.2667, "x": 0.777, "y": 0.657}, {"time": 1.3, "x": 0.475, "y": 0.401}]}, "flare7": {"rotate": [{"time": 1.1, "angle": 5.98}, {"time": 1.1333, "angle": 32.82}], "translate": [{"time": 1.1, "x": -6.34, "y": 112.98}, {"time": 1.1333, "x": 2.66, "y": 111.6}], "scale": [{"time": 1.1, "x": 0.588, "y": 0.588}], "shear": [{"time": 1.1333, "x": -19.93}]}, "flare8": {"rotate": [{"time": 1.2333, "angle": -6.85}], "translate": [{"time": 1.1667, "x": 66.67, "y": 125.52, "curve": "stepped"}, {"time": 1.2, "x": 58.24, "y": 113.53, "curve": "stepped"}, {"time": 1.2333, "x": 40.15, "y": 114.69}], "scale": [{"time": 1.1667, "x": 1.313, "y": 1.203}, {"time": 1.2333, "x": 1.038, "y": 0.95}], "shear": [{"time": 1.2, "y": -13.01}]}, "flare9": {"rotate": [{"time": 1.1667, "angle": 2.9}], "translate": [{"time": 1.1667, "x": 28.45, "y": 151.35, "curve": "stepped"}, {"time": 1.2, "x": 48.8, "y": 191.09, "curve": "stepped"}, {"time": 1.2333, "x": 52, "y": 182.52, "curve": "stepped"}, {"time": 1.2667, "x": 77.01, "y": 195.96}], "scale": [{"time": 1.1667, "x": 0.871, "y": 1.073}, {"time": 1.2, "x": 0.927, "y": 0.944}, {"time": 1.2333, "x": 1.165, "y": 1.336}], "shear": [{"time": 1.1667, "x": 7.95, "y": 25.48}]}, "flare10": {"rotate": [{"time": 1.1667, "angle": 2.18}], "translate": [{"time": 1.1667, "x": 55.64, "y": 137.64, "curve": "stepped"}, {"time": 1.2, "x": 90.49, "y": 151.07, "curve": "stepped"}, {"time": 1.2333, "x": 114.06, "y": 153.05, "curve": "stepped"}, {"time": 1.2667, "x": 90.44, "y": 164.61}], "scale": [{"time": 1.1667, "x": 2.657, "y": 0.891}, {"time": 1.2, "x": 3.314, "y": 1.425}, {"time": 1.2333, "x": 2.871, "y": 0.924}, {"time": 1.2667, "x": 2.317, "y": 0.775}], "shear": [{"time": 1.1667, "x": -1.35}]}}, "deform": {"default": {"torso": {"torso": [{"time": 1.3333}, {"time": 1.4667, "offset": 26, "vertices": [-6.5248, 6.64212, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 3e-05, -1e-05, 0.65784, 8.28917, 0.65787, 8.28917, 1.41232, 5.06703, 1.41235, 5.067, 0, 0, 0, 0, 0.65784, 8.28917, 0.65784, 8.28917, 0.65784, 8.28917, 0.65787, 8.28917, 0.65784, 8.28917, 0.65787, 8.28917, 0.65784, 8.28917, 0.65784, 8.28917, 0.65784, 8.28917, 0.65787, 8.28917, 3e-05, -1e-05, 0, 0, 0, 0, 0, 0, -0.91647, 9.00049, -0.9164, 9.00037, 1.76997, 9.34928, -1.01155, 7.51457, -1.01145, 7.51462], "curve": "stepped"}, {"time": 1.8333, "offset": 26, "vertices": [-6.5248, 6.64212, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 3e-05, -1e-05, 0.65784, 8.28917, 0.65787, 8.28917, 1.41232, 5.06703, 1.41235, 5.067, 0, 0, 0, 0, 0.65784, 8.28917, 0.65784, 8.28917, 0.65784, 8.28917, 0.65787, 8.28917, 0.65784, 8.28917, 0.65787, 8.28917, 0.65784, 8.28917, 0.65784, 8.28917, 0.65784, 8.28917, 0.65787, 8.28917, 3e-05, -1e-05, 0, 0, 0, 0, 0, 0, -0.91647, 9.00049, -0.9164, 9.00037, 1.76997, 9.34928, -1.01155, 7.51457, -1.01145, 7.51462]}, {"time": 2}]}}}}, "run": {"slots": {"mouth": {"attachment": [{"name": "mouth-grind"}]}}, "bones": {"front-thigh": {"rotate": [{"angle": 42.05, "curve": 0.196, "c2": 0.86, "c3": 0.75}, {"time": 0.0667, "angle": 46.08}, {"time": 0.1333, "angle": -20.29}, {"time": 0.2, "angle": -27.24}, {"time": 0.2667, "angle": -47.17}, {"time": 0.3333, "angle": -39.79}, {"time": 0.4, "angle": -25.86}, {"time": 0.4667, "angle": 14.35}, {"time": 0.5333, "angle": 55.63}, {"time": 0.6, "angle": 69.65}, {"time": 0.6667, "angle": 86.41}, {"time": 0.7333, "angle": 65.88}, {"time": 0.8, "angle": 42.05}], "translate": [{}, {"time": 0.0333, "x": -5.8, "y": 11.16}, {"time": 0.0667, "x": -5.13, "y": 11.55}, {"time": 0.1333, "x": -7.7, "y": 8.99}, {"time": 0.5333, "x": -1.26, "y": 3.83}, {"time": 0.8}]}, "torso": {"rotate": [{"angle": -39.71}, {"time": 0.2, "angle": -57.29}, {"time": 0.4, "angle": -39.71}, {"time": 0.6, "angle": -57.29}, {"time": 0.8, "angle": -39.71}]}, "rear-thigh": {"rotate": [{"angle": -56.59}, {"time": 0.0667, "angle": -21.57}, {"time": 0.1333, "angle": 27.95}, {"time": 0.2, "angle": 42.43}, {"time": 0.2667, "angle": 62.37}, {"time": 0.3333, "angle": 45.43}, {"time": 0.4, "angle": 15.67}, {"time": 0.4667, "angle": 28.22}, {"time": 0.5333, "angle": -38.62}, {"time": 0.6, "angle": -53.27}, {"time": 0.6667, "angle": -79.31}, {"time": 0.7333, "angle": -86.47}, {"time": 0.8, "angle": -56.59}], "translate": [{}, {"time": 0.4, "x": -6.76, "y": -3.86}, {"time": 0.4333, "x": -15.85, "y": 7.28}, {"time": 0.4667, "x": -13.05, "y": 4.05}, {"time": 0.5, "x": -10.25, "y": 7.11}, {"time": 0.5333, "x": -9.02, "y": -5.15}, {"time": 0.6667, "x": -23.18, "y": -2.58}, {"time": 0.8}]}, "rear-shin": {"rotate": [{"angle": -74}, {"time": 0.0667, "angle": -83.38}, {"time": 0.1333, "angle": -106.7}, {"time": 0.2, "angle": -66.01}, {"time": 0.2667, "angle": -55.22}, {"time": 0.3333, "angle": -24.8}, {"time": 0.4, "angle": 18.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -56.65}, {"time": 0.5333, "angle": -11.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -41.27}, {"time": 0.7333, "angle": -43.61}, {"time": 0.8, "angle": -74}]}, "front-upper-arm": {"rotate": [{"angle": -89.37}, {"time": 0.0667, "angle": -95.67}, {"time": 0.1333, "angle": -22.01}, {"time": 0.2, "angle": -316.04}, {"time": 0.2667, "angle": -274.94}, {"time": 0.3333, "angle": -273.74}, {"time": 0.4, "angle": -272.09}, {"time": 0.4667, "angle": -264.9}, {"time": 0.5333, "angle": -320.1}, {"time": 0.6, "angle": -50.84}, {"time": 0.6667, "angle": -81.73}, {"time": 0.7333, "angle": -83.92}, {"time": 0.8, "angle": -89.37}], "translate": [{"x": 6.25, "y": 10.05}, {"time": 0.2667, "x": 4.96, "y": -13.13}, {"time": 0.6, "x": -2.43, "y": 1.95}, {"time": 0.8, "x": 6.25, "y": 10.05}]}, "front-bracer": {"rotate": [{"angle": 33.44}, {"time": 0.0667, "angle": 20.54}, {"time": 0.1333, "angle": 15.26}, {"time": 0.2, "angle": 19.29}, {"time": 0.2667, "angle": 22.62}, {"time": 0.3333, "angle": 37.29}, {"time": 0.4, "angle": 41.53}, {"time": 0.4667, "angle": 31.74}, {"time": 0.5333, "angle": 67.45}, {"time": 0.6667, "angle": 39.77}, {"time": 0.7333, "angle": 30.95}, {"time": 0.8, "angle": 33.44}]}, "front-fist": {"rotate": [{"angle": -19.76}, {"time": 0.0667, "angle": -37.11}, {"time": 0.1333, "angle": -50.8}, {"time": 0.2667, "angle": -12.69}, {"time": 0.3333, "angle": 3.01}, {"time": 0.4333, "angle": 12.06}, {"time": 0.5333, "angle": 13.26}, {"time": 0.8, "angle": -19.76}]}, "rear-upper-arm": {"rotate": [{"angle": 68.68}, {"time": 0.0667, "angle": 73.89}, {"time": 0.1333, "angle": -9.64}, {"time": 0.2, "angle": 284.28}, {"time": 0.2667, "angle": 283.29}, {"time": 0.3333, "angle": 278.29}, {"time": 0.4, "angle": 271.03}, {"time": 0.4667, "angle": 263.2}, {"time": 0.5333, "angle": 314.26}, {"time": 0.6, "angle": 16.83}, {"time": 0.6667, "angle": 70.35}, {"time": 0.7333, "angle": 73.54}, {"time": 0.8, "angle": 68.68}], "translate": [{"x": -2.57, "y": -8.89}, {"time": 0.1333, "x": -4.68, "y": 7.21}, {"time": 0.6, "x": 4.33, "y": 2.06}, {"time": 0.8, "x": -2.57, "y": -8.89}]}, "rear-bracer": {"rotate": [{"angle": 31.05}, {"time": 0.0667, "angle": 28.28}, {"time": 0.1333, "angle": 49.36}, {"time": 0.2, "angle": 59.37}, {"time": 0.2667, "angle": 8.56}, {"time": 0.3333, "angle": 9.39}, {"time": 0.4, "angle": 11.51}, {"time": 0.4667, "angle": 7.22}, {"time": 0.5333, "angle": -18.44}, {"time": 0.6, "angle": 11.45}, {"time": 0.6667, "angle": 9.99}, {"time": 0.7333, "angle": 8.29}, {"time": 0.8, "angle": 31.05}]}, "neck": {"rotate": [{"angle": 11.03}, {"time": 0.2, "angle": 13.59}, {"time": 0.4, "angle": 11.03}, {"time": 0.6, "angle": 13.59}, {"time": 0.8, "angle": 11.03}]}, "head": {"rotate": [{"angle": 14.73}, {"time": 0.1, "angle": 12.35}, {"time": 0.2, "angle": 25.55}, {"time": 0.4, "angle": 11.03}, {"time": 0.5, "angle": 12.35}, {"time": 0.6, "angle": 25.55}, {"time": 0.8, "angle": 14.73}]}, "front-shin": {"rotate": [{"curve": 0.481, "c2": 0.01, "c3": 0.75}, {"time": 0.0667, "angle": -64.42}, {"time": 0.1333, "angle": -20.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -62.52}, {"time": 0.3333, "angle": -79.75}, {"time": 0.4, "angle": -78.28}, {"time": 0.4667, "angle": -118.96, "curve": 0.93, "c2": 0.01, "c3": 0.953, "c4": 0.95}, {"time": 0.6, "angle": -88.96}, {"time": 0.6667, "angle": -79.1}, {"time": 0.7333, "angle": -47.78}, {"time": 0.8}]}, "front-foot": {"rotate": [{}, {"time": 0.0333, "angle": -21.13, "curve": 0.121, "c2": 0.24, "c3": 0.75}, {"time": 0.0667, "angle": 17.64}, {"time": 0.1, "angle": 29.93}, {"time": 0.1333, "angle": 16.45}, {"time": 0.2, "angle": -29.23}, {"time": 0.2667, "angle": -1.62}, {"time": 0.3333, "angle": -10.23}, {"time": 0.4667, "angle": -15.99}, {"time": 0.6, "angle": 9.03}, {"time": 0.7333, "angle": 17.33}, {"time": 0.8}]}, "rear-foot": {"rotate": [{}, {"time": 0.0667, "angle": -12.04}, {"time": 0.1333, "angle": -0.87}, {"time": 0.2, "angle": 25.81}, {"time": 0.2667, "angle": 4.71}, {"time": 0.4, "angle": 18.09, "curve": 0.281, "c2": 0.74, "c3": 0.75}, {"time": 0.4333, "angle": -1.71}, {"time": 0.4667, "angle": 27.13}, {"time": 0.5, "angle": 38.84}, {"time": 0.5333, "angle": 30.77}, {"time": 0.5667, "angle": -20.49}, {"time": 0.6, "angle": -30.81}, {"time": 0.6667, "angle": -1.32}, {"time": 0.8}]}, "gun": {"rotate": [{}, {"time": 0.1333, "angle": 24.73}, {"time": 0.5, "angle": -11.88}, {"time": 0.8}]}, "hip": {"translate": [{"y": -24.88, "curve": 0.301, "c2": 0.8, "c3": 0.663, "c4": 0.91}, {"time": 0.0667, "y": -40.28, "curve": 0.456, "c3": 0.339, "c4": 0.99}, {"time": 0.2667, "y": 20.51, "curve": 0.17, "c2": 0.53, "c3": 0.597, "c4": 0.99}, {"time": 0.4, "y": -24.88}, {"time": 0.4333, "y": -26.36}, {"time": 0.4667, "y": -45.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 20.51}, {"time": 0.8, "y": -24.88}]}, "front-foot-target": {"rotate": [{}, {"time": 0.0333, "angle": -41.68}, {"time": 0.1333, "angle": -102.42}, {"time": 0.2, "angle": -121.44}, {"time": 0.2333, "angle": -133.6}, {"time": 0.2667, "angle": -139.86}, {"time": 0.3333, "angle": -152.4}, {"time": 0.3667, "angle": -146.32}, {"time": 0.5, "angle": -143.8}, {"time": 0.5333, "angle": -114.84}, {"time": 0.5667, "angle": -99.09}, {"time": 0.6, "angle": -63.03}, {"time": 0.6333, "angle": -47.35}, {"time": 0.6667, "angle": -31.04}, {"time": 0.7, "angle": -25.02}, {"time": 0.7667, "angle": -15.95}, {"time": 0.8}], "translate": [{"x": 159.32, "y": 38.68}, {"time": 0.0333, "x": 115.32, "y": 0.18}, {"time": 0.0667, "x": 16.34, "y": 0.18}, {"time": 0.1333, "x": -116.47, "y": 0.18}, {"time": 0.2, "x": -210.62, "y": 126.29}, {"time": 0.2333, "x": -226.12, "y": 203.77}, {"time": 0.2667, "x": -223.74, "y": 258.01}, {"time": 0.3333, "x": -208.24, "y": 250.26}, {"time": 0.3667, "x": -207.64, "y": 215.69}, {"time": 0.4, "x": -205.86, "y": 185.3}, {"time": 0.4333, "x": -179.04, "y": 176.95}, {"time": 0.4667, "x": -154, "y": 157.28}, {"time": 0.5, "x": -128.97, "y": 108.41}, {"time": 0.5333, "x": -76.68, "y": 75.29}, {"time": 0.5667, "x": -41.24, "y": 67.74}, {"time": 0.6, "x": 28.48, "y": 59.03}, {"time": 0.6333, "x": 70.89, "y": 78.2}, {"time": 0.6667, "x": 110.42, "y": 99}, {"time": 0.7, "x": 122.21, "y": 79.59}, {"time": 0.7667, "x": 145.33, "y": 44.62}, {"time": 0.8, "x": 159.32, "y": 38.68}]}, "front-leg-target": {"translate": [{"x": -14.25, "y": -25.96}, {"time": 0.1333, "x": -13.64, "y": -34.72}, {"time": 0.1667, "x": -11.42, "y": -12.61}, {"time": 0.5, "x": -14.89, "y": -31.79}, {"time": 0.8, "x": -14.25, "y": -25.96}]}, "rear-foot-target": {"rotate": [{}, {"time": 0.0667, "angle": 18.55}, {"time": 0.1333, "angle": 52.76}, {"time": 0.1667, "angle": 87.4}, {"time": 0.2333, "angle": 133.95}, {"time": 0.3, "angle": 150.92}, {"time": 0.3667, "angle": 168.02}, {"time": 0.4, "angle": 129.09}, {"time": 0.4333, "angle": 125.95}, {"time": 0.5, "angle": 114.27}, {"time": 0.5333, "angle": 85.37}, {"time": 0.5667, "angle": 49.18}, {"time": 0.6333, "angle": 9.51}, {"time": 0.7, "angle": 4.15}, {"time": 0.7667, "angle": -1.37}, {"time": 0.8}], "translate": [{"x": -248.9, "y": 230.07}, {"time": 0.0667, "x": -228.7, "y": 134.12}, {"time": 0.1333, "x": -145.38, "y": 94.22}, {"time": 0.1667, "x": -82.76, "y": 54.33}, {"time": 0.2333, "x": 37.93, "y": 74.39}, {"time": 0.2667, "x": 80.38, "y": 91.82}, {"time": 0.3, "x": 93.21, "y": 67.3}, {"time": 0.3667, "x": 99.34, "y": 35.47}, {"time": 0.4, "x": 68.63, "y": 0.35}, {"time": 0.4333, "x": 21.58, "y": -2.64}, {"time": 0.5, "x": -92.91, "y": -2.64}, {"time": 0.5333, "x": -166.79, "y": -2.64}, {"time": 0.5667, "x": -252.52, "y": 57.15}, {"time": 0.6333, "x": -304.32, "y": 214.03}, {"time": 0.7, "x": -296.92, "y": 281.37}, {"time": 0.7667, "x": -269.54, "y": 257.69}, {"time": 0.8, "x": -248.9, "y": 230.07}]}, "rear-leg-target": {"translate": [{"x": 85, "y": -33.59}]}, "back-foot-tip": {"rotate": [{"angle": -151.52}, {"time": 0.1333, "angle": -93.33}, {"time": 0.1667, "angle": -70.78}, {"time": 0.2333, "angle": 22.43}, {"time": 0.3, "angle": 36.86}, {"time": 0.3667, "angle": 34.85}, {"time": 0.4, "angle": 0.77}, {"time": 0.4333, "angle": 0.83, "curve": "stepped"}, {"time": 0.5333, "angle": 0.83}, {"time": 0.5667, "angle": -61.7}, {"time": 0.6333, "angle": -139.59}, {"time": 0.7, "angle": -146.79}, {"time": 0.8, "angle": -151.52}]}, "front-foot-tip": {"rotate": [{"angle": 42.2}, {"time": 0.0333, "angle": -0.24}, {"time": 0.1333, "angle": -0.28}, {"time": 0.1667, "angle": -59.58}, {"time": 0.2, "angle": -112.55}, {"time": 0.2667, "angle": -130.08}, {"time": 0.3333, "angle": -146.2}, {"time": 0.5, "angle": -86.49}, {"time": 0.5333, "angle": -86.99}, {"time": 0.5667, "angle": -66.87}, {"time": 0.6, "angle": -22.9}, {"time": 0.6333, "angle": -12.07}, {"time": 0.7, "angle": 35.4}, {"time": 0.8, "angle": 42.2}]}, "hair1": {"rotate": [{}, {"time": 0.1, "angle": -10.22}, {"time": 0.2667, "angle": 7.16}, {"time": 0.3667, "angle": -0.15}, {"time": 0.4667, "angle": -10.22}, {"time": 0.6333, "angle": 7.16}, {"time": 0.7333, "angle": -0.15}, {"time": 0.8}]}, "hair2": {"rotate": [{}, {"time": 0.1, "angle": -10.22}, {"time": 0.1667, "angle": -30.13}, {"time": 0.2667, "angle": 6.38}, {"time": 0.3667, "angle": -13.49}, {"time": 0.4667, "angle": -10.22}, {"time": 0.5333, "angle": -30.13}, {"time": 0.6333, "angle": 6.38}, {"time": 0.7333, "angle": -13.49}, {"time": 0.8}]}, "hair3": {"rotate": [{}, {"time": 0.1, "angle": -10.22}, {"time": 0.2667, "angle": 7.16}, {"time": 0.3667, "angle": -0.15}, {"time": 0.4667, "angle": -10.22}, {"time": 0.6333, "angle": 7.16}, {"time": 0.7333, "angle": -0.15}, {"time": 0.8}]}, "hair4": {"rotate": [{}, {"time": 0.1, "angle": -10.22}, {"time": 0.1667, "angle": -30.13}, {"time": 0.2667, "angle": 6.38}, {"time": 0.3667, "angle": -13.49}, {"time": 0.4667, "angle": -10.22}, {"time": 0.5333, "angle": -30.13}, {"time": 0.6333, "angle": 6.38}, {"time": 0.7333, "angle": -13.49}, {"time": 0.8}]}, "torso2": {"rotate": [{"angle": 4.52}]}, "torso3": {"rotate": [{"angle": 4.52}]}}, "deform": {"default": {"eye": {"eye-indifferent": [{"vertices": [-0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867], "curve": 0.25, "c3": 0.75}, {"time": 0.4, "vertices": [3.92969, -18.23849, 3.92969, -18.23849, 3.92969, -18.23849, 3.92969, -18.23849], "curve": 0.25, "c3": 0.75}, {"time": 0.8, "vertices": [-0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867]}]}, "goggles": {"goggles": [{"vertices": [-0.08838, 0.23265, -0.04028, 0.11366, -1.15417, 5.38666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08234, 5.00095, -1.86743, 8.62226, -0.82043, 3.80259, -0.0957, 0.27988, -0.11633, 0.3275, -5.76245, 7.7601, -3.05988, 10.76797, -2.18188, 10.12057, -4.92511, 9.4566, 0, 0, 0, 0, 0.65329, -3.03143, 0.55997, -2.59837, -1.40085, 6.49587, -0.16394, 0.42825, -0.14651, 0.37986, -0.13544, 0.3509, -0.11295, 0.31703, -0.12219, 0.33459, -0.12271, 0.32938, -0.10715, 0.28685, -0.90088, 4.0234, -0.04678, 0.13842, -1.0719, 4.96331, -1.06213, 4.94196, -1.04929, 4.90511, -0.04034, 0.1196, -0.07523, 0.20426, -0.10211, 0.26987, -0.12775, 0.33331, -0.13965, 0.36775, -0.14172, 0.37709, -0.13071, 0.35703, -0.11951, 0.33389, -0.14542, 0.39532, -0.16638, 0.43952, -1.40085, 6.49587, -0.82043, 3.80259, -0.82043, 3.80259, -0.82043, 3.80259, -1.82895, 8.48514, -1.82895, 8.48514, -1.82895, 8.48514], "curve": 0.25, "c3": 0.75}, {"time": 0.4, "vertices": [1.7334, -8.03619, 0.70187, -3.25497, 0.39651, -1.84367, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.08755, -5.04639, 3.97546, -18.45124, 0.47232, -2.1937, 1.59595, -7.39851, 2.05963, -9.54877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.58685, -11.98995, 2.93106, -13.58876, 2.71149, -12.57045, 2.01114, -9.32378, 2.26413, -10.49626, 2.34348, -10.8643, 2.0517, -9.51168, 1.96225, -9.10095, 0.75806, -3.51469, 0.08057, -0.37485, 0.57971, -2.69226, 0.35056, -1.63069, 0.65036, -3.01589, 1.40933, -6.5339, 1.98853, -9.21902, 4.07944, -18.92243, 3.45761, -16.03436, 3.45532, -16.02369, 2.42819, -11.25721, 2.14264, -9.93373, 2.06396, -9.5659, 2.59061, -12.00682, 0, 0, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937], "curve": 0.25, "c3": 0.75}, {"time": 0.8, "vertices": [-0.08838, 0.23265, -0.04028, 0.11366, -1.15417, 5.38666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08234, 5.00095, -1.86743, 8.62226, -0.82043, 3.80259, -0.0957, 0.27988, -0.11633, 0.3275, -5.76245, 7.7601, -3.05988, 10.76797, -2.18188, 10.12057, -4.92511, 9.4566, 0, 0, 0, 0, 0.65329, -3.03143, 0.55997, -2.59837, -1.40085, 6.49587, -0.16394, 0.42825, -0.14651, 0.37986, -0.13544, 0.3509, -0.11295, 0.31703, -0.12219, 0.33459, -0.12271, 0.32938, -0.10715, 0.28685, -0.90088, 4.0234, -0.04678, 0.13842, -1.0719, 4.96331, -1.06213, 4.94196, -1.04929, 4.90511, -0.04034, 0.1196, -0.07523, 0.20426, -0.10211, 0.26987, -0.12775, 0.33331, -0.13965, 0.36775, -0.14172, 0.37709, -0.13071, 0.35703, -0.11951, 0.33389, -0.14542, 0.39532, -0.16638, 0.43952, -1.40085, 6.49587, -0.82043, 3.80259, -0.82043, 3.80259, -0.82043, 3.80259, -1.82895, 8.48514, -1.82895, 8.48514, -1.82895, 8.48514]}]}, "head": {"head": [{"offset": 32, "vertices": [2.81555, 0.98518, 1.01535, 8.62647, -2.70273, 4.09556, -4.48743, 7.13697, -4.76981, 3.34322, 0, 0, -2.25769, -4.31037, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.14777, 14.58548, -2.86661, 13.27987, -2.55057, 11.81706, -2.17331, 10.06675, -1.96667, 9.10786, -2.01523, 9.33308, -2.29977, 10.65304, -2.63971, 12.23277, -3.05856, 14.172, 0, 0, 0, 0, 0, 0, 0, 0, -0.59756, 2.77132, -1.96329, 9.10585, -2.16217, 10.02965], "curve": 0.25, "c3": 0.75}, {"time": 0.4, "offset": 34, "vertices": [3.14838, -14.61261, 3.14838, -14.61261, 3.14838, -14.61261, 0.83426, -3.87112, 0, 0, 0, 0, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17175, -0.81676, 0.17175, -0.81676, 0.17175, -0.81676, 0.17175, -0.81676, 0.17175, -0.81676, 0.17175, -0.81676, 0.17175, -0.81676, 0.17175, -0.81676, 0.17175, -0.81676, 0, 0, 0, 0, 0, 0, 0, 0, 0.55618, -2.58074, 0.41714, -1.93558, 1.04282, -4.83889], "curve": 0.25, "c3": 0.75}, {"time": 0.8, "offset": 32, "vertices": [2.81555, 0.98518, 1.01535, 8.62647, -2.70273, 4.09556, -4.48743, 7.13697, -4.76981, 3.34322, 0, 0, -2.25769, -4.31037, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.14777, 14.58548, -2.86661, 13.27987, -2.55057, 11.81706, -2.17331, 10.06675, -1.96667, 9.10786, -2.01523, 9.33308, -2.29977, 10.65304, -2.63971, 12.23277, -3.05856, 14.172, 0, 0, 0, 0, 0, 0, 0, 0, -0.59756, 2.77132, -1.96329, 9.10585, -2.16217, 10.02965]}]}, "mouth": {"mouth-grind": [{"vertices": [-10.19202, 11.7786, -1.60019, 14.33763, 0.02328, 8.88684, -8.56857, 6.32779], "curve": 0.25, "c3": 0.75}, {"time": 0.4, "vertices": [-1.86761, -2.71146, 0.01212, -11.43619, 0.01212, -11.43619, -1.86761, -2.71146], "curve": 0.25, "c3": 0.75}, {"time": 0.8, "vertices": [-10.19202, 11.7786, -1.60019, 14.33763, 0.02328, 8.88684, -8.56857, 6.32779]}]}, "torso": {"torso": [{"offset": 10, "vertices": [6.35965, 1.33517, 6.35962, 1.33517, 6.35965, 1.33517, 6.35962, 1.33517, 0, 0, 0, 0, 0, 0, 0.82059, 5.12242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 3e-05, -1e-05, 0.82059, 5.12243, 0.82062, 5.12241, 0.82059, 5.12243, 0.82062, 5.12241, 1.43295, 3.92841, 1.43304, 3.92826, 0.82059, 5.12242, 0.82059, 5.12243, 0.82059, 5.12243, 0.82062, 5.12241, 0.24155, 4.36882, 0.24158, 4.36882, 0.24156, 4.36882, 0.24155, 4.36882, 0.24156, 4.36882, 3e-05, -1e-05, 0.82062, 5.12241, -0.77553, 4.89196, 0, 0, 0, 0, -0.80437, 5.76189, -0.80463, 5.76189, 0.687, 7.31474, -0.35934, 5.4162, -0.35928, 5.41616]}, {"time": 0.4, "offset": 2, "vertices": [1.46152, 2.96601, 1.46152, 2.966, 0.68634, 3.23446, 0.68634, 3.23445, 2.20619, 0.10388, 2.20618, 0.10388, 0, 0, 0, 0, -0.31029, -2.89859, -0.31027, -2.8986, 0, 0, -0.1851, 0.38208, 0.33795, -3.61552, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0.23715, 2.56816, 0.23701, 2.56804, 0.23724, 2.56822, 0.39799, 4.23787, 0.39807, 4.23792, -0.55164, 4.21406, -0.55157, 4.21406, 3e-05, -1e-05, 3e-05, -1e-05, -0.29404, -8.94628, -0.29398, -8.94629, -0.02417, -9.50224, -0.02417, -9.50224, 0.23018, -9.9391, 0.23019, -9.9391, -4.64136, -8.88914, -4.64133, -8.88915, -2.62137, -9.24012, -2.62134, -9.24013, -1.70071, -5.16261, -1.70071, -5.16262, -1.70074, -5.16261, -1.70071, -5.16261, -1.70074, -5.16261, 3e-05, -1e-05, -7.37057, -10.47318, 1.06334, -5.92199, 0, 0, 0, 0, -0.49225, -2.67543, -0.49225, -2.67542, 3.36296, -7.48156, -2.08173, -6.76357, -2.08174, -6.76364]}, {"time": 0.8, "offset": 10, "vertices": [6.35965, 1.33517, 6.35962, 1.33517, 6.35965, 1.33517, 6.35962, 1.33517, 0, 0, 0, 0, 0, 0, 0.82059, 5.12242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 0, 0, 3e-05, -1e-05, 3e-05, -1e-05, 3e-05, -1e-05, 0.82059, 5.12243, 0.82062, 5.12241, 0.82059, 5.12243, 0.82062, 5.12241, 1.43295, 3.92841, 1.43304, 3.92826, 0.82059, 5.12242, 0.82059, 5.12243, 0.82059, 5.12243, 0.82062, 5.12241, 0.24155, 4.36882, 0.24158, 4.36882, 0.24156, 4.36882, 0.24155, 4.36882, 0.24156, 4.36882, 3e-05, -1e-05, 0.82062, 5.12241, -0.77553, 4.89196, 0, 0, 0, 0, -0.80437, 5.76189, -0.80463, 5.76189, 0.687, 7.31474, -0.35934, 5.4162, -0.35928, 5.41616]}]}}}, "events": [{"name": "footstep"}, {"time": 0.3667, "name": "footstep"}]}, "run-to-idle": {"slots": {"front-fist": {"attachment": [{"name": "front-fist-open"}]}}, "bones": {"front-foot-target": {"translate": [{"x": -16.5, "y": 3.41}, {"time": 0.1333, "x": -69.06}]}, "hip": {"translate": [{"x": -28.78, "y": -72.96, "curve": 0.507, "c2": 0.21, "c3": 0.607}, {"time": 0.2667, "x": -7.16, "y": -23.15}]}, "rear-foot-target": {"translate": [{"x": 33.15, "y": 31.61}, {"time": 0.0667, "x": 24.41, "y": -3.54}, {"time": 0.2667, "x": 48.87}]}, "front-upper-arm": {"rotate": [{"angle": -80.61}, {"time": 0.2667, "angle": -70.59}]}, "front-bracer": {"rotate": [{"angle": 8.79}, {"time": 0.2667, "angle": 42.09}]}, "rear-upper-arm": {"rotate": [{"angle": 55.3}, {"time": 0.2667, "angle": 39.2}]}, "head": {"rotate": [{}, {"time": 0.2667, "angle": -8.95}]}, "front-fist": {"rotate": [{"angle": 38.26}, {"time": 0.2667}], "scale": [{"x": 0.844}, {"time": 0.2667}]}, "rear-bracer": {"rotate": [{"angle": 57.24}, {"time": 0.2667}]}, "gun": {"rotate": [{"angle": 2.28}, {"time": 0.2667}]}, "torso": {"rotate": [{"angle": -12.98}, {"time": 0.2667, "angle": -8.85}], "scale": [{"x": 0.963, "y": 1.074, "curve": 0.25, "c3": 0.494}, {"time": 0.2667}]}, "neck": {"rotate": [{}, {"time": 0.2667, "angle": 3.78}]}, "hair3": {"rotate": [{}, {"time": 0.1333, "angle": -8.67}, {"time": 0.2667}]}, "hair4": {"rotate": [{}, {"time": 0.1333, "angle": -13.07}, {"time": 0.2667}]}, "hair1": {"rotate": [{}, {"time": 0.1333, "angle": -9.73}, {"time": 0.2667}]}, "hair2": {"rotate": [{}, {"time": 0.1333, "angle": -0.14}, {"time": 0.2667}]}}}, "shoot": {"slots": {"muzzle": {"color": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff62"}], "attachment": [{"time": 0.0333, "name": "muzzle01"}, {"time": 0.0667, "name": "muzzle02"}, {"time": 0.1, "name": "muzzle03"}, {"time": 0.1333, "name": "muzzle04"}, {"time": 0.1667, "name": "muzzle05"}, {"time": 0.2, "name": null}]}, "muzzle-glow": {"color": [{"color": "ff0c0c00"}, {"time": 0.0333, "color": "ffc9adff", "curve": 0.831, "c2": 0.04, "c3": 0.899, "c4": 0.73}, {"time": 0.3, "color": "ff400cff"}, {"time": 0.6333, "color": "ff0c0c00"}], "attachment": [{"name": "muzzle-glow"}]}, "muzzle-ring": {"color": [{"time": 0.0333, "color": "d8baffff", "curve": 0.846, "c3": 0.903, "c4": 0.79}, {"time": 0.2333, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2333, "name": null}]}, "muzzle-ring2": {"color": [{"time": 0.0333, "color": "d8baffff", "curve": 0.846, "c3": 0.903, "c4": 0.79}, {"time": 0.2, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2, "name": null}]}, "muzzle-ring3": {"color": [{"time": 0.0333, "color": "d8baffff", "curve": 0.846, "c3": 0.903, "c4": 0.79}, {"time": 0.2, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2, "name": null}]}, "muzzle-ring4": {"color": [{"time": 0.0333, "color": "d8baffff", "curve": 0.846, "c3": 0.903, "c4": 0.79}, {"time": 0.2, "color": "d7baff00"}], "attachment": [{"time": 0.0333, "name": "muzzle-ring"}, {"time": 0.2, "name": null}]}}, "bones": {"gun": {"rotate": [{"time": 0.0667, "curve": 0.419, "c2": 0.64, "c3": 0.778, "c4": 0.95}, {"time": 0.1333, "angle": 45.35, "curve": 0.069, "c2": 0.51, "c3": 0.75}, {"time": 0.6333}]}, "muzzle": {"translate": [{"x": -11.02, "y": 25.16}]}, "rear-upper-arm": {"translate": [{"time": 0.0333}, {"time": 0.1, "x": 4.74, "y": 9.98}, {"time": 0.2333}]}, "rear-bracer": {"translate": [{"time": 0.0333}, {"time": 0.1, "x": -4.36, "y": -2.88}, {"time": 0.2333}]}, "gun-tip": {"translate": [{}, {"time": 0.3, "x": 3.15, "y": 0.39}], "scale": [{"x": 0.366, "y": 0.366}, {"time": 0.0333, "x": 1.453, "y": 1.453}, {"time": 0.3, "x": 0.366, "y": 0.366}]}, "muzzle-ring": {"translate": [{"time": 0.0333}, {"time": 0.2333, "x": 64.47}], "scale": [{"time": 0.0333}, {"time": 0.2333, "x": 5.951, "y": 5.951}]}, "muzzle-ring2": {"translate": [{"time": 0.0333}, {"time": 0.2, "x": 172.57}], "scale": [{"time": 0.0333}, {"time": 0.2, "x": 4, "y": 4}]}, "muzzle-ring3": {"translate": [{"time": 0.0333}, {"time": 0.2, "x": 277.17}], "scale": [{"time": 0.0333}, {"time": 0.2, "x": 2, "y": 2}]}, "muzzle-ring4": {"translate": [{"time": 0.0333}, {"time": 0.2, "x": 392.06}]}}}, "walk": {"bones": {"rear-foot-target": {"rotate": [{"angle": -32.82}, {"time": 0.1, "angle": -77.14}, {"time": 0.2, "angle": -73.32}, {"time": 0.4333, "angle": 30.49}, {"time": 0.5, "angle": -0.28, "curve": "stepped"}, {"time": 0.6667, "angle": -0.28}, {"time": 0.7667, "angle": -33.78}, {"time": 0.8667, "angle": -32.82}], "translate": [{"x": -167.32, "y": 0.12}, {"time": 0.1, "x": -205.81, "y": 42.58}, {"time": 0.2, "x": -119.04, "y": 61.48, "curve": 0.296, "c2": 0.33, "c3": 0.634, "c4": 0.67}, {"time": 0.4333, "x": 92.52, "y": 26.2}, {"time": 0.5, "x": 47.15, "y": -0.96}, {"time": 0.5333, "x": 27.23, "y": -0.86}, {"time": 0.6667, "x": -42.87, "y": -0.52}, {"time": 0.7667, "x": -110.82, "y": -0.18}, {"time": 0.8667, "x": -167.32, "y": 0.12}]}, "front-foot-target": {"rotate": [{"angle": 29.01}, {"time": 0.0667, "angle": -0.28, "curve": "stepped"}, {"time": 0.1, "angle": -0.28}, {"time": 0.2}, {"time": 0.3333, "angle": -28.33}, {"time": 0.4333, "angle": -43.6}, {"time": 0.5333, "angle": -78.46}, {"time": 0.6667, "angle": -80.78}, {"time": 0.7667, "angle": -36.75}, {"time": 0.8667, "angle": 29.01}], "translate": [{"x": 153.74, "y": 27.82}, {"time": 0.0667, "x": 109.33, "y": -0.52}, {"time": 0.1, "x": 91.43, "y": -0.43}, {"time": 0.2, "x": 36.13, "y": -0.15}, {"time": 0.3333, "x": -38.12, "y": 0.22}, {"time": 0.4333, "x": -94.33, "y": 0.5}, {"time": 0.5333, "x": -136.78, "y": 57.05}, {"time": 0.6667, "x": -54.53, "y": 69.29}, {"time": 0.8667, "x": 153.74, "y": 27.82}]}, "hip": {"translate": [{"x": 3.42, "y": -16.2}, {"time": 0.1, "x": 13.57, "y": -20.63, "curve": 0.548, "c3": 0.75}, {"time": 0.3333, "x": 6.91, "y": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 6.54, "y": -14.78}, {"time": 0.5333, "x": 6.83, "y": -19.85, "curve": 0.548, "c3": 0.75}, {"time": 0.7667, "x": 6.91, "y": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 3.42, "y": -16.2}]}, "front-foot-tip": {"rotate": [{"angle": 28.96}, {"time": 0.0667, "angle": 0.82}, {"time": 0.1, "angle": 1.68, "curve": "stepped"}, {"time": 0.4333, "angle": 1.68}, {"time": 0.5333, "angle": -59.66}, {"time": 0.6667, "angle": -94.92}, {"time": 0.7667, "angle": -35.84}, {"time": 0.8667, "angle": 28.96}]}, "torso": {"rotate": [{"angle": -20.72}, {"time": 0.2, "angle": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -20.72, "curve": 0.136, "c2": 0.36, "c3": 0.75}, {"time": 0.6667, "angle": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -20.72}]}, "neck": {"rotate": [{"angle": 18.06}, {"time": 0.2, "angle": 12.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.4}, {"time": 0.4333, "angle": 18.06, "curve": 0.168, "c2": 0.27, "c3": 0.75}, {"time": 0.6667, "angle": 12.81, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 15.95}, {"time": 0.8667, "angle": 18.06}]}, "head": {"rotate": [{"angle": 4.88}, {"time": 0.2, "angle": 12.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.4}, {"time": 0.4333, "angle": 18.06, "curve": 0.168, "c2": 0.27, "c3": 0.75}, {"time": 0.6667, "angle": 12.81, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 15.95}, {"time": 0.8667, "angle": 4.88}]}, "back-foot-tip": {"rotate": [{}, {"time": 0.1, "angle": -59.01}, {"time": 0.2, "angle": -99.81}, {"time": 0.3333, "angle": -28.38}, {"time": 0.4333, "angle": 48.63}, {"time": 0.5, "angle": 0.85}, {"time": 0.8667}]}, "front-thigh": {"rotate": [{"angle": 41.32}], "translate": [{"x": 15.47, "y": -0.08}, {"time": 0.1, "x": 9.94, "y": -2.81}, {"time": 0.2, "x": 4.34, "y": 0.72}, {"time": 0.3333, "x": 0.02, "y": -1.11}, {"time": 0.4333, "x": -4.26, "y": 0.02}, {"time": 0.5333, "x": 1.53, "y": -1.94}, {"time": 0.6667, "x": 8.32, "y": -5.38}, {"time": 0.7667, "x": 6.11, "y": -4.87}, {"time": 0.8667, "x": 15.47, "y": -0.08}]}, "rear-thigh": {"rotate": [{"angle": -32.3}], "translate": [{"x": -24.88, "y": 0.12}, {"time": 0.2, "x": -10.72, "y": -1.15}, {"time": 0.4333, "x": -1.33, "y": 0.01}, {"time": 0.6667, "x": -16.28, "y": 0.08}, {"time": 0.7667, "x": -20.18, "y": 0.1}, {"time": 0.8667, "x": -24.88, "y": 0.12}]}, "torso2": {"rotate": [{"angle": -5}, {"time": 0.2, "angle": -15.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5, "curve": 0.136, "c2": 0.36, "c3": 0.75}, {"time": 0.6667, "angle": -15.91, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -5}]}, "torso3": {"rotate": [{"angle": -4.68}, {"time": 0.2, "angle": -19.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.68, "curve": 0.136, "c2": 0.36, "c3": 0.75}, {"time": 0.6667, "angle": -19.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -4.68}]}, "front-upper-arm": {"rotate": [{"angle": -9.51}, {"time": 0.1, "angle": -19.4, "curve": 0.482, "c3": 0.645, "c4": 1.09}, {"time": 0.4667, "angle": -303.86, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -9.51}], "translate": [{"x": 1.46, "y": 3.5}, {"time": 0.2, "x": -5.92, "y": 4.93}, {"time": 0.4333, "x": -5.24, "y": -4.38}, {"time": 0.6667, "x": -7.69, "y": -8.62}, {"time": 0.8667, "x": 1.46, "y": 3.5}]}, "front-bracer": {"rotate": [{"angle": 1.95}, {"time": 0.1, "angle": 18.36, "curve": 0.246, "c3": 0.645, "c4": 1.09}, {"time": 0.4667, "angle": 24.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 1.95}]}, "front-fist": {"rotate": [{"angle": -28.48}, {"time": 0.1, "angle": -27, "curve": 0.25, "c3": 0.645, "c4": 1.09}, {"time": 0.3333, "angle": -33.94, "curve": 0.407, "c2": -0.01, "c3": 0.75}, {"time": 0.5333, "angle": 3.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -28.48}]}, "rear-upper-arm": {"rotate": [{"angle": 28.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 22.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 326.34}, {"time": 0.5667, "angle": 312.87, "curve": 0.407, "c2": -0.01, "c3": 0.75}, {"time": 0.7, "angle": -6.78, "curve": 0.407, "c2": -0.01, "c3": 0.75}, {"time": 0.8667, "angle": 28.28}], "translate": [{"x": -0.18, "y": 1.45}, {"time": 0.2, "x": 0.72, "y": 2.17}, {"time": 0.4333, "x": 16.77, "y": 19.95}, {"time": 0.8667, "x": -0.18, "y": 1.45}]}, "hair2": {"rotate": [{"angle": 18.54}, {"time": 0.1, "angle": 1.97}, {"time": 0.2, "angle": -5.65}, {"time": 0.4333, "angle": 24.96}, {"time": 0.6333, "angle": -6.26}, {"time": 0.8667, "angle": 18.54}]}, "hair4": {"rotate": [{"angle": 1.97}, {"time": 0.1, "angle": -5.65}, {"time": 0.3333, "angle": 24.96}, {"time": 0.5333, "angle": -6.26}, {"time": 0.7667, "angle": 18.54}, {"time": 0.8667, "angle": 1.97}]}, "rear-bracer": {"rotate": [{"angle": 10.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 11.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.66}, {"time": 0.5667, "angle": -1.27, "curve": 0.407, "c2": -0.01, "c3": 0.75}, {"time": 0.7, "angle": -4.16, "curve": 0.407, "c2": -0.01, "c3": 0.75}, {"time": 0.8667, "angle": 10.06}]}, "gun": {"rotate": [{"angle": -14.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 18.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.77}, {"time": 0.5667, "angle": 12.57, "curve": 0.407, "c2": -0.01, "c3": 0.75}, {"time": 0.7, "angle": -8.69, "curve": 0.407, "c2": -0.01, "c3": 0.75}, {"time": 0.8667, "angle": -14.67}]}, "rear-shin": {"rotate": [{"angle": -5}]}, "rear-foot": {"rotate": [{"angle": 3.52}]}, "aim-constraint-target": {"rotate": [{"angle": -3.19}]}, "front-shin": {"rotate": [{"angle": -10.44}]}, "front-foot": {"rotate": [{"angle": -0.79}]}}, "deform": {"default": {"eye": {"eye-indifferent": [{"vertices": [-0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867], "curve": "stepped"}, {"time": 0.1333, "vertices": [-0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [3.92969, -18.23849, 3.92969, -18.23849, 3.92969, -18.23849, 3.92969, -18.23849], "curve": "stepped"}, {"time": 0.6, "vertices": [3.92969, -18.23849, 3.92969, -18.23849, 3.92969, -18.23849, 3.92969, -18.23849], "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "vertices": [-0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867, -0.15329, 0.70867]}]}, "goggles": {"goggles": [{"vertices": [-0.08838, 0.23265, -0.04028, 0.11366, -1.15417, 5.38666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08234, 5.00095, -1.86743, 8.62226, -0.82043, 3.80259, -0.0957, 0.27988, -0.11633, 0.3275, -5.76245, 7.7601, -3.05988, 10.76797, -2.18188, 10.12057, -4.92511, 9.4566, 0, 0, 0, 0, 0.65329, -3.03143, 0.55997, -2.59837, -1.40085, 6.49587, -0.16394, 0.42825, -0.14651, 0.37986, -0.13544, 0.3509, 0.70346, 4.33792, 0.69421, 4.35548, 0.6937, 4.35027, 0.70926, 4.30774, -0.90088, 4.0234, -0.04678, 0.13842, -1.0719, 4.96331, -1.06213, 4.94196, -1.04929, 4.90511, -0.04034, 0.1196, -0.07523, 0.20426, -0.10211, 0.26987, -0.12775, 0.33331, -0.13965, 0.36775, -0.14172, 0.37709, -0.13071, 0.35703, -0.11951, 0.33389, -0.14542, 0.39532, -0.16638, 0.43952, -1.40085, 6.49587, -0.82043, 3.80259, -0.82043, 3.80259, -0.82043, 3.80259, -1.82895, 8.48514, -1.82895, 8.48514, -1.82895, 8.48514], "curve": "stepped"}, {"time": 0.1333, "vertices": [-0.08838, 0.23265, -0.04028, 0.11366, -1.15417, 5.38666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08234, 5.00095, -1.86743, 8.62226, -0.82043, 3.80259, -0.0957, 0.27988, -0.11633, 0.3275, -5.76245, 7.7601, -3.05988, 10.76797, -2.18188, 10.12057, -4.92511, 9.4566, 0, 0, 0, 0, 0.65329, -3.03143, 0.55997, -2.59837, -1.40085, 6.49587, -0.16394, 0.42825, -0.14651, 0.37986, -0.13544, 0.3509, 0.70346, 4.33792, 0.69421, 4.35548, 0.6937, 4.35027, 0.70926, 4.30774, -0.90088, 4.0234, -0.04678, 0.13842, -1.0719, 4.96331, -1.06213, 4.94196, -1.04929, 4.90511, -0.04034, 0.1196, -0.07523, 0.20426, -0.10211, 0.26987, -0.12775, 0.33331, -0.13965, 0.36775, -0.14172, 0.37709, -0.13071, 0.35703, -0.11951, 0.33389, -0.14542, 0.39532, -0.16638, 0.43952, -1.40085, 6.49587, -0.82043, 3.80259, -0.82043, 3.80259, -0.82043, 3.80259, -1.82895, 8.48514, -1.82895, 8.48514, -1.82895, 8.48514], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [0.72116, -13.02245, -0.08078, -15.10208, 0.5881, -9.07231, 0, 0, -0.95035, 2.12869, -4.29099, 4.74269, -0.37964, -1.86985, -0.50616, -2.49316, 2.05878, -14.16591, 3.97546, -18.45124, 0.47232, -2.1937, 1.59595, -7.39851, 2.05963, -9.54877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.58685, -11.98995, 2.93106, -13.58876, 2.71149, -12.57045, 1.12061, -13.71136, 1.3736, -14.88384, 1.45294, -15.25188, 1.16116, -13.89926, 0.95001, -14.08721, -0.25418, -8.50095, -0.4256, -2.86804, 0.72946, -6.04102, 2.13202, -10.56477, -0.57986, -18.66593, -1.0582, -18.68787, 1.98853, -9.21902, 2.82358, -21.9123, 3.45761, -16.03436, 3.45532, -16.02369, 2.42819, -11.25721, 2.14264, -9.93373, 2.06396, -9.5659, 2.59061, -12.00682, 0, 0, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, -0.53992, -7.17996], "curve": "stepped"}, {"time": 0.6, "vertices": [0.72116, -13.02245, -0.08078, -15.10208, 0.5881, -9.07231, 0, 0, -0.95035, 2.12869, -4.29099, 4.74269, -0.37964, -1.86985, -0.50616, -2.49316, 2.05878, -14.16591, 3.97546, -18.45124, 0.47232, -2.1937, 1.59595, -7.39851, 2.05963, -9.54877, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.58685, -11.98995, 2.93106, -13.58876, 2.71149, -12.57045, 1.12061, -13.71136, 1.3736, -14.88384, 1.45294, -15.25188, 1.16116, -13.89926, 0.95001, -14.08721, -0.25418, -8.50095, -0.4256, -2.86804, 0.72946, -6.04102, 2.13202, -10.56477, -0.57986, -18.66593, -1.0582, -18.68787, 1.98853, -9.21902, 2.82358, -21.9123, 3.45761, -16.03436, 3.45532, -16.02369, 2.42819, -11.25721, 2.14264, -9.93373, 2.06396, -9.5659, 2.59061, -12.00682, 0, 0, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, 0.47232, -2.1937, -0.53992, -7.17996], "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "vertices": [-0.08838, 0.23265, -0.04028, 0.11366, -1.15417, 5.38666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08234, 5.00095, -1.86743, 8.62226, -0.82043, 3.80259, -0.0957, 0.27988, -0.11633, 0.3275, -5.76245, 7.7601, -3.05988, 10.76797, -2.18188, 10.12057, -4.92511, 9.4566, 0, 0, 0, 0, 0.65329, -3.03143, 0.55997, -2.59837, -1.40085, 6.49587, -0.16394, 0.42825, -0.14651, 0.37986, -0.13544, 0.3509, 0.70346, 4.33792, 0.69421, 4.35548, 0.6937, 4.35027, 0.70926, 4.30774, -0.90088, 4.0234, -0.04678, 0.13842, -1.0719, 4.96331, -1.06213, 4.94196, -1.04929, 4.90511, -0.04034, 0.1196, -0.07523, 0.20426, -0.10211, 0.26987, -0.12775, 0.33331, -0.13965, 0.36775, -0.14172, 0.37709, -0.13071, 0.35703, -0.11951, 0.33389, -0.14542, 0.39532, -0.16638, 0.43952, -1.40085, 6.49587, -0.82043, 3.80259, -0.82043, 3.80259, -0.82043, 3.80259, -1.82895, 8.48514, -1.82895, 8.48514, -1.82895, 8.48514]}]}, "head": {"head": [{"offset": 8, "vertices": [2.09991, 9.25076, 8.45337, 4.30371, -3.35175, 8.87419, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.81555, 0.98518, 1.01535, 8.62647, -2.70273, 4.09556, -4.48743, 7.13697, -4.76981, 3.34322, 0, 0, -2.25769, -4.31037, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.13202, 7.95453, 2.22333, 9.79501, 8.95061, 4.55695, -3.54895, 9.39622, -6.13202, 7.95453, -3.54895, 9.39622, -3.54895, 9.39622, 8.95061, 4.55695, 0, 0, 3.18365, 15.68383, 14.26176, 7.26074, -5.65479, 14.97183, 3.18365, 15.68383, 0, 0, 0, 0, 1.99811, 9.84312, -6.13202, 7.95453, -3.54895, 9.39622, 0, 0, 0, 0, 2.3309, 11.48366, 0, 0, 0, 0, 0, 0, 2.66449, 13.12421, 0, 0, -3.14777, 14.58548, -2.86661, 13.27987, -2.55057, 11.81706, -2.17331, 10.06675, -1.96667, 9.10786, -2.01523, 9.33308, -2.29977, 10.65304, -2.63971, 12.23277, -3.05856, 14.172, 0, 0, 0, 0, 0, 0, 0, 0, -0.59756, 2.77132, -1.96329, 9.10585, -2.16217, 10.02965], "curve": "stepped"}, {"time": 0.1333, "offset": 8, "vertices": [2.09991, 9.25076, 8.45337, 4.30371, -3.35175, 8.87419, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.81555, 0.98518, 1.01535, 8.62647, -2.70273, 4.09556, -4.48743, 7.13697, -4.76981, 3.34322, 0, 0, -2.25769, -4.31037, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.13202, 7.95453, 2.22333, 9.79501, 8.95061, 4.55695, -3.54895, 9.39622, -6.13202, 7.95453, -3.54895, 9.39622, -3.54895, 9.39622, 8.95061, 4.55695, 0, 0, 3.18365, 15.68383, 14.26176, 7.26074, -5.65479, 14.97183, 3.18365, 15.68383, 0, 0, 0, 0, 1.99811, 9.84312, -6.13202, 7.95453, -3.54895, 9.39622, 0, 0, 0, 0, 2.3309, 11.48366, 0, 0, 0, 0, 0, 0, 2.66449, 13.12421, 0, 0, -3.14777, 14.58548, -2.86661, 13.27987, -2.55057, 11.81706, -2.17331, 10.06675, -1.96667, 9.10786, -2.01523, 9.33308, -2.29977, 10.65304, -2.63971, 12.23277, -3.05856, 14.172, 0, 0, 0, 0, 0, 0, 0, 0, -0.59756, 2.77132, -1.96329, 9.10585, -2.16217, 10.02965], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "offset": 34, "vertices": [3.14838, -14.61261, 3.14838, -14.61261, 3.14838, -14.61261, 0.83426, -3.87112, 0, 0, 0, 0, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -1.59174, -7.84007, -0.89545, -4.41003, -0.89545, -4.41003, -1.59174, -7.84007, 0.55618, -2.58074, 0.41714, -1.93558, 1.04282, -4.83889], "curve": "stepped"}, {"time": 0.6, "offset": 34, "vertices": [3.14838, -14.61261, 3.14838, -14.61261, 3.14838, -14.61261, 0.83426, -3.87112, 0, 0, 0, 0, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -0.72369, -5.22679, -1.59174, -7.84007, -0.89545, -4.41003, -0.89545, -4.41003, -1.59174, -7.84007, 0.55618, -2.58074, 0.41714, -1.93558, 1.04282, -4.83889]}, {"time": 0.8667, "offset": 8, "vertices": [2.09991, 9.25076, 8.45337, 4.30371, -3.35175, 8.87419, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.81555, 0.98518, 1.01535, 8.62647, -2.70273, 4.09556, -4.48743, 7.13697, -4.76981, 3.34322, 0, 0, -2.25769, -4.31037, 0, 0, 0, 0, -0.45578, 2.11445, -0.45578, 2.11445, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.13202, 7.95453, 2.22333, 9.79501, 8.95061, 4.55695, -3.54895, 9.39622, -6.13202, 7.95453, -3.54895, 9.39622, -3.54895, 9.39622, 8.95061, 4.55695, 0, 0, 3.18365, 15.68383, 14.26176, 7.26074, -5.65479, 14.97183, 3.18365, 15.68383, 0, 0, 0, 0, 1.99811, 9.84312, -6.13202, 7.95453, -3.54895, 9.39622, 0, 0, 0, 0, 2.3309, 11.48366, 0, 0, 0, 0, 0, 0, 2.66449, 13.12421, 0, 0, -3.14777, 14.58548, -2.86661, 13.27987, -2.55057, 11.81706, -2.17331, 10.06675, -1.96667, 9.10786, -2.01523, 9.33308, -2.29977, 10.65304, -2.63971, 12.23277, -3.05856, 14.172, 0, 0, 0, 0, 0, 0, 0, 0, -0.59756, 2.77132, -1.96329, 9.10585, -2.16217, 10.02965]}]}, "mouth": {"mouth-grind": [{"vertices": [-10.19202, 11.7786, -1.60019, 14.33763, 0.02328, 8.88684, -8.56857, 6.32779], "curve": "stepped"}, {"time": 0.1333, "vertices": [-10.19202, 11.7786, -1.60019, 14.33763, 0.02328, 8.88684, -8.56857, 6.32779], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [-1.87524, -8.97547, 0.00449, -17.7002, 0.00449, -17.7002, -1.87524, -8.97547], "curve": "stepped"}, {"time": 0.6, "vertices": [-1.87524, -8.97547, 0.00449, -17.7002, 0.00449, -17.7002, -1.87524, -8.97547], "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "vertices": [-10.19202, 11.7786, -1.60019, 14.33763, 0.02328, 8.88684, -8.56857, 6.32779]}], "mouth-smile": [{"vertices": [-6.59216, 5.02815, 5.28665, -1.62104, 2.43057, -7.10703, -6.07846, 8.24725], "curve": "stepped"}, {"time": 0.1333, "vertices": [-6.59216, 5.02815, 5.28665, -1.62104, 2.43057, -7.10703, -6.07846, 8.24725], "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "vertices": [1.95737, -8.63879, 0.58041, -17.27288, 1.98795, -27.30994, -8.04211, -23.88625], "curve": "stepped"}, {"time": 0.6, "vertices": [1.95737, -8.63879, 0.58041, -17.27288, 1.98795, -27.30994, -8.04211, -23.88625], "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "vertices": [-6.59216, 5.02815, 5.28665, -1.62104, 2.43057, -7.10703, -6.07846, 8.24725]}]}, "torso": {"torso": [{"offset": 24, "vertices": [0.99754, -8.62222, -4.36671, -11.12821, 3.38991, -3.5328, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.00336, 4.8839, -1.39807, 4.78593, 0, 0, 0, 0, 0, 0, 0, 0, 0.99754, -8.62222, 0, 0, 0, 0, 0.41353, -3.58589, -0.58401, 5.03633, -1.02026, 4.96621, 0, 0, 0, 0, -0.61319, 2.98462, 0.39218, -3.38733, 0.68637, -3.34027, -1.63116, 5.58357]}, {"time": 0.1, "vertices": [-1.87766, 0.23508, 10.64218, 3.4945, 8.76065, 8.13096, 7.4079, 0.46964, 6.52606, 4.22304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46204, -2.67851, -1.00093, -5.80334, -0.61595, -3.57126, 0.15442, -3.62069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.95602, 6.51617, -0.21823, 8.17005, 0.60684, 0.26677, 0.45453, 0.48326, 2.96719, 0.85007, 2.5141, 1.78982, 1.42711, 0.95876, 1.02582, 1.37934, 0.9938, 8.43367, -2.3866, 8.1498, 1.32321, 11.29527, -2.3905, 11.22245, -0.27824, 3.32372, -1.36951, 3.04126, -0.69302, -4.01772, -1.54007, 8.31738, -0.07013, 9.53309, 0.51686, 2.99771, 0.51686, 2.99771, -0.12991, 3.03919, 1.17288, 12.46493, -2.98672, 12.23994, 1.91373, 6.46839, -0.23099, -1.33925, 0.05792, -1.35778, -2.41547, 12.32078]}, {"time": 0.2, "vertices": [0.13651, -3.42358, 14.41745, 0.02832, 13.25629, 5.67007, 12.89688, -0.65636, 12.12503, 4.44476, 0, 0, 0, 0, 0, 0, 0, 0, -0.12337, 0.36149, -0.237, 0.29979, -0.16426, 3.2699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.74475, 6.80592, 6.30356, 10.07764, 0.60684, 0.26677, 0.45453, 0.48326, 2.96719, 0.85007, 2.5141, 1.78982, 1.42711, 0.95876, 1.02582, 1.37934, 0.9938, 8.43367, -2.3866, 8.1498, 1.55508, 5.86423, -0.86441, 6.00507, -0.27824, 3.32372, -1.36951, 3.04126, 0, 0, -0.14114, 3.53476, 2.55927, 6.99835, -0.29503, 1.56245, 0, 0, 0, 0, 1.40475, 7.03388, -1.46063, 7.02255, 1.91373, 6.46839, 0, 0, 0, 0, -1.77957, 10.14687]}, {"time": 0.4333, "offset": 2, "vertices": [-1.25909, 6.12791, -1.75449, 6.0049, -1.25909, 6.12791, -1.75449, 6.0049, -0.72083, 6.21444, -1.25909, 6.12791, -0.72083, 6.21444, -1.25909, 6.12791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74069, -3.60475, 1.03217, -3.53232, 0.74069, -3.60475, 1.03217, -3.53232, 0.42329, -3.65553, 0.74069, -3.60475]}, {"time": 0.5333, "offset": 2, "vertices": [-0.19458, 10.61421, -1.69006, 10.61533, -0.19458, 10.61421, -1.69006, 10.61533, -0.72083, 6.21444, -1.25909, 6.12791, -0.72083, 6.21444, -1.25909, 6.12791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.14001, -9.69365, 2.7449, -9.38902, 1.25098, -11.38506, 3.2207, -11.01592, 0.42329, -3.65553, 0.74069, -3.60475, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.237, -4.22984]}, {"time": 0.6667, "offset": 2, "vertices": [-1.25909, 6.12791, -1.75449, 6.0049, -1.25909, 6.12791, -1.75449, 6.0049, -0.72083, 6.21444, -1.25909, 6.12791, -0.72083, 6.21444, -1.25909, 6.12791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.74069, -3.60475, 1.03217, -3.53232, 0.74069, -3.60475, 1.03217, -3.53232, 0.42329, -3.65553, 0.74069, -3.60475]}, {"time": 0.8667, "offset": 24, "vertices": [0.99754, -8.62222, -4.36671, -11.12821, 3.38991, -3.5328, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.00336, 4.8839, -1.39807, 4.78593, 0, 0, 0, 0, 0, 0, 0, 0, 0.99754, -8.62222, 0, 0, 0, 0, 0.41353, -3.58589, -0.58401, 5.03633, -1.02026, 4.96621, 0, 0, 0, 0, -0.61319, 2.98462, 0.39218, -3.38733, 0.68637, -3.34027, -1.63116, 5.58357]}]}}}, "events": [{"name": "footstep"}, {"time": 0.4333, "name": "footstep"}]}}}